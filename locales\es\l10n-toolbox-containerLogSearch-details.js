/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Title
  // Search Bar
  KEYWORD: 'Keyword',
  EXACT_QUERY: 'Exact Query',
  FUZZY_QUERY: 'Fuzzy Query',
  // Time Topology
  TIME_TOPOLOGY: 'Time Topology ',
  SEARCH_RESULTS: 'Search Results',
  DISPLAY: 'Display',
  HIDE: 'Hide',
  STOP_REAL_TIME_CONTAINER_LOG: 'Pause real-time container logs',
  START_REAL_TIME_CONTAINER_LOG: 'View real-time container logs',
  // Refresh Rate
  REFRESH_RATE_COLON: 'Refresh Rate: ',
  // Time Topology > Histogram
  CONTAINER_LOG_COUNT: 'Registros de contenedores',
  // List
  LOG: 'Log',
  // List > Container Log Details
  // List > Container Log Details > Container Log Source
  CONTAINER_LOG_SOURCE: 'Container Log Source',
};
