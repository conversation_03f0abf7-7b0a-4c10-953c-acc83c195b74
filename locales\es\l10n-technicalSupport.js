/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  TECHNICAL_SUPPORT: 'Technical Support',
  LEARN_MORE_CONTACT_US:
    'For more cloud-native technology empowerment services, please <a class="support-action" href={contactUsLink} target="_blank">Contact Us</a>.',
  // Community Support
  COMMUNITY_SUPPORT: 'Community Support',
  FORUM: 'Forum',
  // Ticket Support
  TICKET_SUPPORT: 'Ticket Support',
  TICKET_SUPPORT_DESC:
    'Professional cloud-native development team and container service delivery team with more than 10 years of public and private cloud support experience and in-depth understanding of industries, providing comprehensive technical support including installation, troubleshooting, recovery, and other professional services.',
  CONTACT_US: 'Contact Us',
  // KubeSphere Enterprise
  KUBESPHERE_ENTERPRISE_CAPTION: 'KubeSphere Enterprise',
  KUBE_SPHERE_ENTERPRISE_DESC:
    'KubeSphere Enterprise is an enterprise-grade container platform developed by QingCloud based on the open-source KubeSphere platform. Compared with the open-source edition, KubeSphere Enterprise enhances capabilities for enterprise quantitative operations, large-scale cluster O&M, and security hardening, and extends multiple key features such as microservice governance and app management. The KubeSphere Enterprise Extension Marketplace provides enterprise-grade extensions to meet requirements of various business scenarios, with expert solutions and technical support from QingCloud.',
  KSE_FEATURE_1: 'Empowers enterprise digital transformation towards quantitative operations',
  KSE_FEATURE_2: 'Enhances large-scale federated cluster O&M',
  KSE_FEATURE_3: 'Provides full lifecycle security protection',
  KSE_PLATFORM_VERSION: 'Platform Version: <strong>{version}</strong>',
  // KubeSphere Cloud
  KUBESPHERE_CLOUD_CAPTION: 'KubeSphere Cloud',
  KUBESPHERE_CLOUD_DESC:
    'KubeSphere Cloud builds a digital ecosystem focusing on Kubernetes and provides cloud-native products and services of high standard, such as KubeSphere Backup, KubeSphere Inspector, and KubeSphere Lite, helping enterprises manage, protect, and deliver production-ready applications more efficiently.',
  KSC_FEATURE_BACKUP: 'KubeSphere Backup',
  KSC_FEATURE_BACKUP_APP_DESC: 'Application-level data protection',
  KSC_FEATURE_BACKUP_DATA_DESC: 'Assured data consistency',
  KSC_FEATURE_BACKUP_CLOUD_DESC: 'Unified management across clouds',
  KSC_FEATURE_INSPECTION: 'KubeSphere Inspection',
  KSC_FEATURE_INSPECTION_VULN_DESC: 'Comprehensive vulnerability scanning',
  KSC_FEATURE_INSPECTION_SECURITY_DESC: 'System security hardening',
  KSC_FEATURE_INSPECTION_BEST_DESC: 'Best practice guidance',
  KSC_FEATURE_LIGHTWEIGHT: 'KubeSphere Lite',
  KSC_FEATURE_LIGHTWEIGHT_START_DESC: 'Cluster setup in seconds',
  KSC_FEATURE_LIGHTWEIGHT_KS_DESC: 'Out-of-the-box KubeSphere cluster',
  KSC_FEATURE_LIGHTWEIGHT_TEST_DESC: 'Ideal for testing and demonstration',
  START_NOW: 'Start Now',
};
