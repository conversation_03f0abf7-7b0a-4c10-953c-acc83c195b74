/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  CUSTOM_SERVICE: 'Custom Service',
  VIRTUAL_IP: 'VirtualIP',
  // Resource Status
  CONTAINER_PORT_SCAP: 'Container port',
  SERVICE_PORT_SCAP: 'Service port',
  NO_AVAILABLE_RESOURCE_VALUE: 'No Available {resource}',
  NO_RESOURCE_FOUND: 'No Resource Found',
  REVISION_RECORD: 'Revision record',
  // Scheduling Information
  // Metadata
  // Monitoring
  // Environment Variables
  // Events
};
