/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  FILE_DIRECTORY: 'Directorio de Archivos',
  UPLOAD_FILE: 'Subir Archivo',
  ONLY_SUPPORT_UPLOAD_FILE: 'Solo Soporte Subir Archivo',
  PLEASE_ENTER_FILE_DIRECTORY: 'Por Favor Ingrese el Directorio de Archivos',
  PLEASE_SELECT_FILE_SAVE_PATH_AND_NAME:
    'Por Favor Establezca el Nombre del Archivo y la Ruta de Guardado',
  FILE_SIZE_CANNOT_EXCEED_1G: 'El Tamaño del Archivo No Puede Exceder 1G',
  PLEASE_SELECT_FILE: 'Por Favor Seleccione Archivo',
  NEED_TAR:
    'La imagen base del contenedor debe contener tar para implementar las funciones de carga y descarga de archivos.',
  UP_NEED_TAR:
    'La imagen base del contenedor debe contener tar para implementar la función de carga de archivos; el directorio de destino debe existir y el usuario de la imagen debe tener permisos de lectura y escritura en el directorio.',
  DOWN_NEED_TAR:
    'La imagen base del contenedor debe contener tar para implementar la función de descarga de archivos.',
};
