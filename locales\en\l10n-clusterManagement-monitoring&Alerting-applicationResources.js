/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  APPLICATION_RESOURCE_PL: 'Application Resources',
  MONITORING_APPLICATION_DESC:
    'Application resources provide the monitoring data of application resource usage and usage ranking.',
  // Resource Usage > Cluster Resource Usage
  CUSTOM_TIME_RANGE: 'Customize Time Range',
  ACCOUNTS: 'Accounts',
  DEVOPS_PROJECT_PL: 'DevOps Projects',
  SAMPLING_INTERVAL: 'Sampling Interval',
  KUBE_SUN: 'Sun.',
  KUBE_MON: 'Mon.',
  KUBE_TUE: 'Tue.',
  KUBE_WED: 'Wed.',
  KUBE_THU: 'Thu.',
  KUBE_FRI: 'Fri.',
  KUBE_SAT: 'Sat.',
  KUBE_SUNDAY: 'Sunday',
  KUBE_MONDAY: 'Monday',
  KUBE_TUESDAY: 'Tuesday',
  KU<PERSON>_WEDNESDAY: 'Wednesday',
  KUBE_THURSDAY: 'Thursday',
  KU<PERSON>_FRIDAY: 'Friday',
  KUBE_SATURDAY: 'Saturday',
  KUBE_JAN: 'Jan.',
  KUBE_FEB: 'Feb.',
  KUBE_MAR: 'Mar.',
  KUBE_APR: 'Apr.',
  KUBE_MAY: 'May.',
  KUBE_JUN: 'Jun.',
  KUBE_JUL: 'Jul.',
  KUBE_AUG: 'Aug.',
  KUBE_SEP: 'Sep.',
  KUBE_OCT: 'Oct.',
  KUBE_NOV: 'Nov.',
  KUBE_DEC: 'Dec.',
  KUBE_JAN_LH: 'January',
  KUBE_FEB_LH: 'February',
  KUBE_MAR_LH: 'March',
  KUBE_APR_LH: 'April',
  KUBE_MAY_LH: 'May',
  KUBE_JUN_LH: 'June',
  KUBE_JUL_LH: 'July',
  KUBE_AUG_LH: 'August',
  KUBE_SEP_LH: 'September',
  KUBE_OCT_LH: 'October',
  KUBE_NOV_LH: 'November',
  KUBE_DEC_LH: 'December',
  // Resource Usage > Application Resource Usage
  APPLICATION_RESOURCE_USAGE: 'Application Resource Usage',
  DEPLOYMENT: 'Deployment',
  DEPLOYMENT_PL: 'Deployments',
  DEPLOYMENT_LOW: 'deployment',
  STATEFULSET: 'Statefulset',
  STATEFULSET_PL: 'Statefulsets',
  STATEFULSET_LOW: 'statefulset',
  DAEMONSET: 'Daemonset',
  DAEMONSET_PL: 'Daemonsets',
  DAEMONSET_LOW: 'daemonset',
  DEPLOYMENTS_VALUE: 'Deployments: {value}',
  STATEFULSETS_VALUE: 'Statefulsets: {value}',
  DAEMONSETS_VALUE: 'Daemonsets: {value}',
  RUNNING_PODS: 'Running Pods',
  ROUTE: 'Ingress',
  // Resource Usage > Projects
  PROJECT_PL: 'Projects',
  PROJECT_COUNT: 'Projects',
  // Usage Ranking
  USAGE_RANKING: 'Usage Ranking',
  QUOTA_VALUE: 'Quota: {value}',
  OUTBOUND_TRAFFIC: 'Outbound Traffic',
  INBOUND_TRAFFIC: 'Inbound Traffic',
  SORT_BY_NAMESPACE_CPU_USAGE: 'Sort by CPU usage',
  SORT_BY_NAMESPACE_POD_COUNT: 'Sort by pod quota usage (%)',
  SORT_BY_NAMESPACE_NET_BYTES_RECEIVED: 'Sort by inbound traffic',
  SORT_BY_NAMESPACE_NET_BYTES_TRANSMITTED: 'Sort by outbound traffic',
};
