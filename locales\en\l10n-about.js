/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  KS_DESCRIPTION:
    'KubeSphere is an open-source project aiming to provide enterprise-grade multi-tenant container platform on top of Kubernetes, the current mainstream container orchestration platform. It provides easy-to-use interface and wizard-style operations, reducing the learning curve and operating cost of Kubernetes in terms of the daily work of development, test and maintenance.',
  REPS_ADDRESS: 'GitHub',
  ISSUE_FEEDBACK: 'Feedback',
  PART_IN_DISCUSSION: 'Discussion',
  CODE_CONTRIBUTE: 'Contribution',
  GITHUB_STAR: 'Star',
};
