name: ManuallyCrossBuild

on:
  workflow_dispatch:

jobs:
  build-and-push:
    runs-on: self-hosted

    strategy:
      matrix:
        node-version: [12.18.x]

    steps:
      - uses: actions/checkout@v2

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install Dependencies
        run: npm i -g yarn && yarn

      - name: Build
        run: yarn build

      - name: Test
        run: yarn test

      - name: Set up QEMU
        if: github.event_name == 'push'
        uses: docker/setup-qemu-action@v1

      - name: Set up Docker Buildx
        if: github.event_name == 'push'
        id: buildx
        uses: docker/setup-buildx-action@v1
        with:
          version: v0.7.1
          install: true

      - name: Docker login
        if: github.event_name == 'push'
        uses: docker/login-action@v1
        with:
          registry: docker.io
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Push image
        if: github.event_name == 'push'
        run: |
          TAG=$(echo "${{ github.ref }}" | sed -e 's,.*/\(.*\),\1,') REPO=docker.io/kubespheredev make container-cross-push

      - name: slack
        uses: 8398a7/action-slack@v3
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow,job,took
        if: failure()
