/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // More > Edit Settings
  // More > Edit Settings > Service Settings
  // More > Edit Settings > Cluster Differences
  // More > Edit YAML
  // More > Delete
  // Attributes
  // Resource Status
  // Resource Status > Instance Status
  // esource Status > Pods
  // Service Access
  ACCESS_INFORMATION: 'Acceso al servicio',
  VIRTUAL_IP_ADDRESS: 'Virtual IP address',
  INTERNAL_DOMAIN_NAME_SCAP: 'Método de acceso dentro del clúster (DNS)',
  NODE_PORT_SCAP: 'Node port',
};
