/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  JOB_PL: 'Jobs',
  JOB_DESC:
    'Jobs are used to perform short-lived, one-off tasks. A job creates one or more pods and ensures that a specific number of pods successfully terminate.',
  // List
  JOB_EMPTY_DESC: 'Please create a job.',
  JOB_COMPLETED: 'Completed',
  JOB_FAILED: 'Failed',
  JOB_RUNNING: 'Running',
  LAST_RUN_TIME: 'Last Run Time',
  // List > Create > Basic Information
  // List > Create > Strategy Settings
  // List > Create > Pod Settings
  RESTART_POLICY_NEVER_DESC: 'Re-create pod',
  RESTART_POLICY_ONFAILURE_DESC: 'Restart container',
  // List > Create > Storage Settings
  // List > Create > Advanced Settings
  // List > Edit Information
  // List > Rerun
  RERUN: 'Rerun',
  OPERATION_SUCCESS: 'Operation successful',
  OPERATION_FAILED: 'Operation failed',
  // List > Delete
  JOB: 'Job',
  <PERSON><PERSON><PERSON>_<PERSON><PERSON>: 'job',
};
