/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  PLATFORM: 'Platform',
  CLUSTER_MANAGEMENT: 'Cluster Management',
  CLUSTER_DESC:
    'Unified management of multiple clusters and their basic resources, components, and application resources.',
  ACCESS_CONTROL_DESC: 'Unified management of workspaces, users, and roles.',
  APP_STORE_MANAGEMENT_DESC:
    'Unified lifecycle management of cloud native applications including their release, activation, and suspension.',
  PLATFORM_SETTINGS_DESC:
    'Customized platform settings including basic information and notification configurations.',
};
