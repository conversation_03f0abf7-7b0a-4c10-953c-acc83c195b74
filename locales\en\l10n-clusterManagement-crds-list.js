/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  CRD: 'CRD',
  CRD_PL: 'CRDs',
  CRD_LOW: 'CRD',
  CRD_DESC:
    'A Custom Resource Definition (CRD) extends Kubernetes by allowing users to create any kind of custom resources. Users can use these CRD objects as they do for built-in resources.',
  // List
  CRD_EMPTY_DESC: 'Please create a CRD.',
  KIND_TCAP: 'Kind',
  SCOPE_TCAP: 'Scope',
};
