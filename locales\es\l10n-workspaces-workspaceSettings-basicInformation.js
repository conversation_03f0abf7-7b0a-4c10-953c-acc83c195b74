/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Navigation Pane
  WORKSPACE_SETTINGS: 'Configuración del espacio de trabajo',
  // Banner
  WORKSPACE_BASIC_INFO_DESC:
    'Basic information provides the overview of the workspace. You can view the basic information of the workspace.',
  WORKSPACE_BASE_INFO_Q1: '¿Cómo solicitar más clústeres para el espacio de trabajo?',
  WORKSPACE_BASE_INFO_A1:
    'Contact the platform or cluster administrator to apply for more clusters.',
  // Workspace Information
  WORKSPACE_INFO: 'Información del espacio de trabajo',
  WORKSPACE_MEMBER_TCAP: 'Workspace member',
  WORKSPACE_MEMBER_TCAP_PL: 'Workspace members',
  WS_MEMBER_SCAP: 'Workspace member',
  WS_MEMBER_SCAP_PL: 'Workspace members',
  DEVOPS_PROJECT_TCAP: 'DevOps project',
  DEVOPS_PROJECT_TCAP_PL: 'DevOps projects',
  DEVOPS_PROJECT_LOW: 'DevOps project',
  DEVOPS_PROJECT_LOW_PL: 'DevOps projects',
  PROJECTS: 'Projects',
  // Workspace Information > Edit Information
  // Network Isolation
  ON: 'On',
  OFF: 'Off',
  WS_NETWORK_ISOLATION: 'Aislamiento de red de espacio de trabajo',
  NETWORK_POLICY_UNINSATLLED_DESC: 'The network policy component is not installed in this cluster.',
  // Delete Workspace
  DELETE_DESC:
    'Are you sure you want to delete the resource? The resource cannot be restored after it is deleted.',
  // Delete Workspace > Delete
  DELETE_WORKSPACE_PROJECTS_DESC: 'Eliminar el proyecto asociado con el espacio empresarial',
  DELETE_WORKSPACE_DESC:
    'El espacio de trabajo no se puede restaurar después de ser eliminado y los recursos en el espacio de trabajo también se eliminarán.',
  DELETE_WORKSPACE_TIP:
    '¿Estás seguro de eliminar el espacio de trabajo <strong>{resource}</strong> ? No podrás recuperarlo, y los recursos en el espacio de trabajo también se eliminarán.',
};
