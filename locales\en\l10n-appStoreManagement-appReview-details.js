/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  APP_DETAILS_DESC: 'View details of the app to be released.',
  // Description
  // App Information
  INTRODUCTION: 'Introduction',
  SERVICE_PROVIDER_WEBSITE: 'Service provider website',
  APP_DESCRIPTION: 'App Description',
  // Documentation
  DOCUMENTATION: 'Documentation',
  NO_DOCUMENT_FOUND: 'No Documentation Found',
  UPDATE_TIME_COLON: 'Update time: ',
  // Chart Files
  // Update Log
  UPDATE_LOG: 'Update Log',
  NO_UPDATE_LOG_DESC: 'No update log is found.',
  // Reject
  REJECT: 'Reject',
  REJECT_REASON_DESC: 'Enter reasons for rejection of the app review.',
  REJECT_REASON_TIP: 'Please enter at least one reason for rejection.',
  REJECT_SUCCESSFUL: 'Rejected successfully.',
  // Release
  APPROVE: 'Approve',
  RELEASE_SUCCESSFUL: 'Released successfully.',
};
