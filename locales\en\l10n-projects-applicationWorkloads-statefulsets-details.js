/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // More > Roll Back
  // More > Edit Service
  SELECTOR: 'Selector',
  // More > Edit Settings > Update Strategy
  // More > Edit Settings > Containers
  // More > Edit Settings > Volumes > Add Persistent Volume Template
  // More > Edit Settings > Volumes > Mount Volume
  // More > Edit Settings > Volumes > Mount Configmap or Secret
  // More > Edit Settings > Pod Scheduling Rules
  // More > Re-Create
  RECREATE: 'Re-create',
  RECREATE_SUCCESS_DESC: 'Re-created successfully.',
  // Attributes
  // Resource Status
  // Revision Records
  // Metadata
  // Monitoring
  // Environment Variables
  // Events
};
