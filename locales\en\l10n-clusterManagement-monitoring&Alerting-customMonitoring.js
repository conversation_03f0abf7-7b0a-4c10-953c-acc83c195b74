/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  CUSTOM_MONITORING: 'Custom Monitoring',
  CUSTOM_MONITORING_DASHBOARD: 'Custom Monitoring Dashboard',
  CUSTOM_MONITORING_DASHBOARD_PL: 'Custom Monitoring Dashboards',
  CUSTOM_MONITORING_DASHBOARD_EMPTY_DESC: 'Please create a custom monitoring dashboard.',
  CUSTOM_MONITORING_DASHBOARD_DESC:
    'Custom monitoring provides application monitoring templates. You can customize monitoring dashboards based on your needs.',
  // List

  // List > Create
  TEMPLATE: 'Template',
  CREATE_CUSTOM_MONITORING_DASHBOARD: 'Create Custom Monitoring Dashboard',
  MONITORING_TEMPLATE: 'Monitoring Template',
  CUSTOM_MONITORING_TEMPLATE_DESC:
    'Select a default template, upload a template, or customize a template to generate a custom monitoring dashboard.',
  // List > Create > Grafana
  UPLOAD_GRAFANA_DASHBOARD: 'Upload Grafana Dashboard',
  SUPPORT_JSON_FILE: 'Only files in JSON format are supported.',
  UPLOAD_GRAFANA_URL: 'Upload a Grafana dashboard from URL.',
  UPLOAD_FROM_LOCAL_TITLE: 'Select or Drag a File',
  FILE_UPLOAD_ERROR: 'Only one file can be uploaded.',
  UPLOAD_FILE_TIP: 'Please upload a file.',
  ENTER_GRAFANA_URL: 'Please enter a Grafana dashboard URL.',
  UPLOAD_FROM_LOCAL_STORAGE: 'Upload from Local Storage',
  UPLOAD_FROM_URL: 'Upload from URL',
  // List > Create > Custom
  DASHBOARD_TITILE: 'Dashboard title',
  APPLICABLE_SCENE: 'Applicable Scenario',
  BASE_LINE_CHART: 'Base Line Chart',
  STACK_LINE_CHART: 'Stacked Area Chart',
  BASE_LINE_CHART_DESC:
    'A line chart is mainly used to visualize a trend or change in data over time. It is very useful for displaying a series of two-dimensional continuous data, such as website traffic or product prices.',
  STACK_LINE_CHART_DESC:
    'A stacked area chart is a special kind of area charts that can be used to compare multiple variables in an interval. It is very useful when multiple data series are available as you can analyze the relation of each group to the whole chart and display their respective proportion.',
  LINE_CHART_DESC: 'A line chart is mainly used to visualize a trend or change in data over time.',
  BASE_BAR_CHART: 'Base Bar Chart',
  STACK_BAR_CHART: 'Stacked Bar Chart',
  BAR_CHART_DESC:
    'A bar chart is the most common type of charts. It presents different categorical data with horizontal or vertical bars with heights or lengths proportional to the values that they represent.',
  BASE_BAR_CHART_DESC:
    'One axis of the base bar chart features the categories being compared, while the other axis represents the value of each.',
  STACK_BAR_CHART_DESC:
    'A stacked bar chart is an extension of bar charts. A standard bar chart compares individual data points with each other while in a stacked bar chart, parts of the data are adjacent or stacked. It can be used to present the total amount of a category as well as its sub-amounts (proportion). Therefore, it is very helpful for examining part-to-whole relations.',
  CUSTOM_DISPLAY_MODAL_DESC: 'Customize the table display style according to your needs',
  THRESHOLD_FILL_DESC:
    'You can set a threshold and the style can be changed automatically after the threshold is exceeded.',
  ADD_MONITOR_ITEM: 'Add Monitoring Item',
  ADD_MONITOR_ROW: 'Add Monitoring Group',
  CHART_TYPES: 'Chart Types',
  GRAPH_TYPES: 'Graph Types',
  LINE_CHART: 'Line Chart',
  BAR_CHART: 'Bar Chart',
  SELECT_CHART_TYPE: 'Select a Chart Type',
  SELECT_CHART_TYPE_MODAL_DESC: 'Select a customized chart type',
  SINGLE_STATE_CHART: 'Real-time Text',
  DISPLAY_POSITION: 'Display Position',
  EMPTY_CHART_PLACEHOLDER: 'The chart is displayed here.',
  DISPLAY_FORMAT: 'Display Format',
  FIELD_NAME: 'Field Name',
  COLUMN_NAME: 'Column Name',
  GRAPH_NAME: 'Chart Name',
  DECIMALS: 'Decimal Places',
  TABLE: 'Table',
  TABLE_SETTINGS: 'Table Settings',
  VALUE_FOMATER: 'Value Format',
  PER_PAGE_LINES: 'Lines Per Page',
  CUSTOM_DISPLAY_STYLE: 'Display Style',
  DATA_TYPE: 'Data Type',
  Y_AXIS: 'Y Axis',
  GRAPH_COLORS: 'Chart Colors',
  SINGLE_GRAPH_TYPE_NAME: 'Basic Chart',
  SINGLE_GRAPH_TYPE: 'The most common chart type',
  STACKED_GRAPH_TYPE: 'Stacked Chart',
  STACKED_GRAPH_TYPE_DESC: 'Useful for displaying part-to-whole relations',
  MONITOR_TYPE_NO_SUPPORT: 'The type is not supported currently',
  MONITOR_METRIC: 'Monitoring Metric',
  METRIC_NAME: 'Metric Name',
  DEBUGB_DATA: 'Debugging Data',
  TIME_FORMAT: 'Time Format',
  HIGHT_RULES: 'Highlighting Rules',
  EDIT_TEMPLATE: 'Edit Template',
  SAVE_TEMPLATE: 'Save Template',
  THRESHOLD_FILL: 'Threshold Settings',
  UNIT: 'Unit',
  COOL_COLORS: 'Cool Colors',
  WARM_COLORS: 'Warm Colors',
  DEFAULT_COLORS: 'Default Colors',
  LAST: 'Last',
  SECOND_TIME: '{count, plural, =1 {1 second} other{# seconds}}',
  MINUTE_TIME: '{count, plural, =1 {1 minute} other{# minutes}}',
  HOUR_TIME: '{count, plural, =1 {1 hour} other{# hours}}',
  DAY_TIME: '{count, plural, =1 {1 day} other{# days}}',
  WEEK_TIME: '{count, plural, =1 {1 week} other{# weeks}}',
  NO_REFRESHING: 'No refreshing',
  INTERVAL: 'Interval',
  // List > Edit Information

  // List > Edit YAMl

  // List > Delete
  CUSTOM_MONITORING_DASHBOARD_LOW: 'custom monitoring dashbord',
};
