/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  // More > Edit Settings
  // More > Edit Settings > Volume Settings
  // More > Edit Settings > Cluster Differences
  // More > Edit YAML
  // More > Delete
  // Resource Status
  VOLUME_MONITORING_TIP:
    'KubeSphere collects volume usage data, excluding data from unmounted volumes. For path-based volumes such as OpenEBS/Local PV and NFS, the data collected may be different from the actual amount. For detailed information, see <a href="https://github.com/kubesphere/kubesphere/issues/2921" target="_blank">volume monitoring data analysis</a>.',
  // Mount Information
  MOUNT_INFORMATION: 'Información de montaje',
};
