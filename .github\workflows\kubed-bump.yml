name: Bump @kubed/*

on:
  workflow_dispatch:

jobs:
  kubed-bump:
    runs-on: ubuntu-latest
    steps:
      - name: 'Checkout'
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 16

      - name: Install Yarn
        run: npm install --global yarn

      - name: Install dependencies
        run: yarn install

      - name: Bump @kubed/*
        run: yarn run kubed:bump

      - name: Update yarn.lock
        run: yarn install

      - name: 'Create Pull Request'
        env:
          COMMIT_MESSAGE: 'chore(bot): bump @kubed/*'
        uses: peter-evans/create-pull-request@v6
        with:
          commit-message: ${{ env.COMMIT_MESSAGE }}
          signoff: true
          delete-branch: true
          branch-suffix: timestamp
          title: ${{ env.COMMIT_MESSAGE }}
          body: |
            ```release-note
            none
            ```
          labels: approved
