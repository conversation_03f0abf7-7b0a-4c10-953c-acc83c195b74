/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  FILE_DIRECTORY: 'File Directory',
  UPLOAD_FILE: 'Upload File',
  ONLY_SUPPORT_UPLOAD_FILE: 'Only Support Upload File',
  PLEASE_ENTER_FILE_DIRECTORY: 'Please Enter File Directory',
  PLEASE_SELECT_FILE_SAVE_PATH_AND_NAME: 'Please Set File Name and Save Path',
  FILE_SIZE_CANNOT_EXCEED_1G: 'File Size Cannot Exceed 1G',
  PLEASE_SELECT_FILE: 'Please Select File',
  NEED_TAR:
    'The container base image must contain tar to implement the file upload and download functions.',
  UP_NEED_TAR:
    'The container base image must contain tar to implement the file upload function; the target directory must exist and the image user must have read and write permissions to the directory.',
  DOWN_NEED_TAR:
    'The container base image must contain tar to implement the file download function.',
};
