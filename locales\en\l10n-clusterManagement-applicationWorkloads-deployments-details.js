/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  CREATOR: 'Creator',
  NO: 'No',
  // More > Roll Back
  // More > Edit Autoscaling
  // More > Edit Settings > Update Strategy
  // More > Edit Settings > Containers
  EDIT_CONTAINER: 'Edit Container',
  // More > Edit Settings > Volumes
  // More > Edit Settings > Pod Scheduling Rules
  // More > Edit YAML
  // More > Re-Create
  // More > Delete
  // Resource Status > Clusters
  MULTI_CLUSTER_RESOURCE_TIP:
    'The current resource is deployed across multiple clusters. You can click a cluster to view the resource settings in the cluster.',
  // Resource Status > Replica Status
  // Resource Status > Ports
  // Resource Status > Pods
  NODE: 'Node',
  // Revision Records
  SERIAL_NUMBER: 'Serial number',
  CREATED_TIME: 'Created at {diff}',
  // Metadata
  // Monitoring
  // Environment Variables
  // Events
  EVENT_PL: 'Events',
  EVENT_NORMAL: 'Normal',
  EVENT_WARNING: 'Warning',
  EVENT_CRITICAL: 'Critical',
};
