/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Title
  BACK: 'Back',
  // Description
  // Install > Deployment Agreement
  AGREE: 'De acuerdo',
  APP_DEPLOY_AGREEMENT: 'Acuerdo de implementación de la aplicación',
  APP_DEPLOY_AGREEMENT_DESC_1: 'You must abide by the open-source agreement of the application.',
  APP_DEPLOY_AGREEMENT_DESC_2:
    'Any consequences arising from your deployment of the application shall be borne by you. For support services, please contact the developer.',
  DO_NOT_REMIND_AGAIN: 'No recordar de nuevo',
  // Install > Basic Information
  LOCATION: 'Ubicación de despliegue',
  CLUSTER_NAME_DESC:
    'The name can contain only lowercase letters, numbers, and hyphens (-), must start with a lowercase letter, and must end with a lowercase letter or number. The maximum length is 32 characters.',
  FEDPROJECT_CANNOT_DEPLOY_APP_TIP:
    'No se pueden implementar aplicaciones en proyectos de múltiples clústeres.',
  LATEST_VERSION_SCAP: 'Última versión',
  WORKSPACE_EMPTY_DESC: 'Selecciona un espacio de trabajo, por favor',
  VERSION_EMPTY_DESC: 'Selecciona una versión',
  // Install > App Settings
  HELM_APP_SCHEMA_FORM_TIP:
    'The app configuration can be displayed in a form. You can modify the default app configuration through either the form or YAML editor. Note: app data stored in different patterns are independent of each other.',
  DEPLOYED_SUCCESSFUL: 'Deployed successfully.',
  // App Information
  APP_INFORMATION: 'Info de Aplicación',
  VERSION_NUMBER: 'Número de versión',
  APP_SCREENSHOTS: 'Capturas de pantalla de Aplicación',
  CATEGORY_COLON: 'Category:',
  HOMEPAGE_COLON: 'Homepage:',
  RELEASE_DATE_COLON: 'Release Date:',
  APP_ID_COLON: 'App ID:',
  SOURCE_CODE_ADDRESS_COLON: 'Source Code Address:',
  APP_VERSIONS_TITLE: 'Versión de la lista',
  // APP_VERSIONS_TITLE: 'Versions (only the latest 10 versions will be displayed)',
  MAINTAINER_COLON: 'Mantenedores',
  // App Details > Verions
  VERSIONS: 'Versions',
  // APP Details > Keywords
  KEYWORDS: 'Palabras clave',
  NONE: 'Ninguno',
  // App Details > App Introduction
  APP_INTRODUCTION: 'App Introduction',
  APP_DETAILS: 'Detalles de Aplicación',
  NO_DOCUMENT_DESC: 'No documentation is found.',
  VERSION_INTRO_EMPTY_DESC: 'La versión no tiene documentación.',
  // App Details > Chart Files
  CHART_FILES: 'Ficheros del Chart',
  NO_APP_CHART_FILE_FOUND: 'La aplicación no tiene un fichero Chart',
};
