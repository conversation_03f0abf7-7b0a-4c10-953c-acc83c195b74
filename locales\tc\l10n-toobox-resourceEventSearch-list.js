/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Title
  TOTAL_EVENTS_TODAY:
    'A total of <span class={className}> {events} </span> resource events were collected today.',
  NO_RESOURCE_EVENTS_TODAY: 'No Resource Events Collected Today',
  // Search
  EVENT_NOT_ENABLED_DESC:
    'The component is not enabled. <a href="{docUrl}/pluggable-components/events/" target="_blank">Learn More</a>',
  // Querying Rules
  RESOURCE_EVENT_TRENDS_12H: 'Resource event trends in 12 h',
  RESOURCE_EVENT_WORKSPACE_TIP: 'Enter a workspace name to search for container logs.',
  RESOURCE_EVENT_PROJECT_TIP: 'Enter a project name to search for container logs.',
  RESOURCE_EVENT_RESOURCE_TYPE_TIP: 'Enter a resource type to search for container logs.',
  RESOURCE_EVENT_RESOURCE_NAME_TIP: 'Enter a resource name to search for container logs.',
  RESOURCE_EVENT_MESSAGE_TIP: 'Enter a message to search for resource events.',
  RESOURCE_EVENT_CATEGORY_TIP: 'Enter a category name to search for container logs.',
  RESOURCE_EVENT_REASON_TIP: 'Enter a reason to search for container logs.',
  SEARCH_BY_MESSAGE: 'Search by Message',
  SEARCH_BY_WORKSPACE: 'Search by Workspace',
  SEARCH_BY_RESOURCE_TYPE: 'Search by Resource Type',
  SEARCH_BY_RESOURCE_NAME: 'Search by Resource Name',
  SEARCH_BY_REASON: 'Search by Reason',
  SEARCH_BY_CATEGORY: 'Search by Category',
};
