/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  // More > Edit YAML
  // More > Clone Volume
  // More > Create Snapshot
  // More > Expand
  VOLUME_EXPAND_TIP:
    'The persistent volume claim has been mounted to a workload. Expanding the persistent volume claim will cause the workload to restart and business will be interrupted for a short while.',
  // More > Delete
  // Resource Status
  // Resource Status > Volume
  // Resource Status > Mounted Pods
  MOUNTED_PODS: 'Mounted Pods',
  // Metadata
  // Events
  // Snapshot
  SNAPSHOT_PL: 'Snapshots',
};
