/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  APPLICATION_WORKLOAD_PL: 'Application Workloads',
  WORKLOAD_PL: 'Workloads',
  // List
  DEPLOYMENTS: 'Deployments',
  UPDATE_TIME_TCAP: 'Update Time',
  ALL_PROJECTS: 'All projects',
  SHOW_NUM: 'Show: {num}',
  // List > Create > Basic Information
  SELECT_PROJECT_DESC: 'Select a project in which the resource is to be created.',
  PROJECT_NOT_SELECT_DESC: 'Please select a project.',
  BASIC_INFORMATION: 'Basic Information',
  NAME: 'Name',
  FEDPROJECT_RESOURCE_TIP:
    'To create workload resources in a multi-cluster project, go to the Workloads page in the multi-cluster project.',
  FINISHED: 'Done',
  NOT_SET: 'To do',
  CURRENT: 'Current',
  PROJECT: 'Project',
  // List > Create > Pod Settings
  PREVIOUS: 'Previous',
  NOTE: 'Note',
  // List > Create > Pod Settings > Add Container > Container Settings
  IMAGE: 'Image',
  IMAGE_VALUE: 'Image: {value}',
  // List > Create > Pod Settings > Add Container > Health Check > Readiness Check > TCP Port
  PORT_NUMBER_EMPTY: 'Please enter a port number.',
  USER: 'User',
  // List > Create > Storage Settings
  VOLUME_NAME_EXIST: 'The volume name already exists.',
  SELECT_TYPE: 'Select {type}',
  SPECIFY_SUBPATH: 'Specify Subpath',
  SPECIFY_SUBPATH_TIP: 'Specify a volume subpath to be mounted to the container.',
  MOUNT_PATH: 'Mount path',
  MOUNT_PATH_NOT_SPECIFIED: 'Please select a key to be mounted and the mount path of the key.',
  MOUNT_PATH_EMPTY: 'Please enter a mount path.',
  MOUNT_PATH_REPEATED: 'The mount path is duplicated.',
  // List > Create > Advanced Settings
  NETWORK_SEGMENT_SCAP: 'Network segment',
  AVAILABLE_ADDRESSES: 'Available addresses',
  POD_IP_POOL: 'Pod IP Pool',
  SUBPATH: 'Subpath',
  // List > Create > Advanced Settings > Add Metadata
  ANNOTATION_PL: 'Annotations',
  CREATE_SUCCESSFUL: 'Created successfully.',
  // List > Edit Information
  // List > Edit YAML
  // List > Re-create
  // List > Delete
  // List > Stop
  STOP: 'Stop',
  STOP_TITLE_SI: 'Stop {type}',
  STOP_TITLE_PL: 'Stop Multiple {type}',
  STOP_DESC: 'Are you sure you want to stop the resource?',
};
