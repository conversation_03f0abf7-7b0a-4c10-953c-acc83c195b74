/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Navigation Pane
  WORKSPACE_SETTINGS: 'Workspace Settings',
  // Banner
  WORKSPACE_BASIC_INFO_DESC:
    'Basic information provides the overview of the workspace. You can view the basic information of the workspace.',
  WORKSPACE_BASE_INFO_Q1: 'How do I apply for more clusters for the workspace?',
  WOR<PERSON><PERSON>CE_BASE_INFO_A1:
    'Contact the platform or cluster administrator to apply for more clusters.',
  // Workspace Information
  WORKSPACE_INFO: 'Workspace Information',
  WORKSPACE_MEMBER_TCAP: 'Workspace member',
  WORKSPACE_MEMBER_TCAP_PL: 'Workspace members',
  WS_MEMBER_SCAP: 'Workspace member',
  WS_MEMBER_SCAP_PL: 'Workspace members',
  DEVOPS_PROJECT_TCAP: 'DevOps project',
  DEVOPS_PROJECT_TCAP_PL: 'DevOps projects',
  DEVOPS_PROJECT_LOW: 'DevOps project',
  DEVOPS_PROJECT_LOW_PL: 'DevOps projects',
  PROJECTS: 'Projects',
  // Workspace Information > Edit Information
  // Network Isolation
  ON: 'On',
  OFF: 'Off',
  WS_NETWORK_ISOLATION: 'Workspace network isolation',
  NETWORK_POLICY_UNINSATLLED_DESC: 'The network policy component is not installed in this cluster.',
  // Delete Workspace
  DELETE_DESC:
    'Are you sure you want to delete the resource? The resource cannot be restored after it is deleted.',
  // Delete Workspace > Delete
  DELETE_WORKSPACE_PROJECTS_DESC: 'Delete projects in the workspace',
  DELETE_WORKSPACE_DESC:
    'The workspace cannot be restored after it is deleted and all resources in the workspace will be removed.',
  DELETE_WORKSPACE_TIP:
    'Are you sure you want to delete the workspace <strong>{resource}</strong>? The workspace cannot be restored after it is deleted and all resources in the workspace will be removed.',
};
