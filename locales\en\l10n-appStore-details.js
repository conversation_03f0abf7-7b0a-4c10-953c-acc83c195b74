/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Title
  BACK: 'Back',
  // Description

  // Install > Deployment Agreement
  AGREE: 'Agree',
  APP_DEPLOY_AGREEMENT: 'Deployment Agreement',
  APP_DEPLOY_AGREEMENT_DESC_1: 'You must abide by the open-source agreement of the application.',
  APP_DEPLOY_AGREEMENT_DESC_2:
    'Any consequences arising from your deployment of the application shall be borne by you. For support services, please contact the developer.',
  DO_NOT_REMIND_AGAIN: 'Do not remind me again',
  // Install > Basic Information
  LOCATION: 'Location',
  CLUSTER_NAME_DESC:
    'The name can contain only lowercase letters, numbers, and hyphens (-), must start with a lowercase letter, and must end with a lowercase letter or number. The maximum length is 32 characters.',
  FEDPROJECT_CANNOT_DEPLOY_APP_TIP: 'The app cannot be deployed in a multi-cluster project.',
  LATEST_VERSION_SCAP: 'Latest version',
  WORKSPACE_EMPTY_DESC: 'Please select a workspace',
  VERSION_EMPTY_DESC: 'Please select a version.',
  // Install > App Settings
  HELM_APP_SCHEMA_FORM_TIP:
    'You can customize the app settings in a GUI form or a YAML file. Settings in the GUI form and those in the YAML file are independent of each other.',
  DEPLOYED_SUCCESSFUL: 'Installed successfully.',
  // App Information
  APP_INFORMATION: 'App Information',
  VERSION_NUMBER: 'Version Number',
  APP_SCREENSHOTS: 'App Screenshots',
  CATEGORY_COLON: 'Category:',
  HOMEPAGE_COLON: 'Homepage:',
  RELEASE_DATE_COLON: 'Release Date:',
  APP_ID_COLON: 'App ID:',
  SOURCE_CODE_ADDRESS_COLON: 'Source Code Address:',
  APP_VERSIONS_TITLE: 'Listing version',
  // APP_VERSIONS_TITLE: 'Versions (only the latest 10 versions will be displayed)',
  MAINTAINER_COLON: 'Maintainer:',
  // App Details > Verions
  VERSIONS: 'Versions',
  // APP Details > Keywords
  KEYWORDS: 'Keywords',
  NONE: 'None',
  // App Details > App Introduction
  APP_INTRODUCTION: 'App Introduction',
  APP_DETAILS: 'App Details',
  NO_DOCUMENT_DESC: 'No documentation is found.',
  VERSION_INTRO_EMPTY_DESC: 'No introduction to this version is found.',
  // App Details > Chart Files
  CHART_FILES: 'Chart Files',
  NO_APP_CHART_FILE_FOUND: 'No chart file of the app is found.',
};
