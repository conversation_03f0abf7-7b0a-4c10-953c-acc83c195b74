/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Add Log Receiver > OpenSearch
  OPENSEARCH_DESC:
    'You can use the OpenSearch log receiver to ingest log data into an OpenSearch database. To use this feature, you need to deploy OpenSearch in advance.',
  LOG_COLLECTION_OS_URL_TIPS: 'IP address or hostname of the target OpenSearch instance',
  LOG_COLLECTION_OS_USER_TIPS: 'Optional username credential for access',
  LOG_COLLECTION_OS_PASSWORD_TIPS: 'Password for user defined in HTTP_User',
};
