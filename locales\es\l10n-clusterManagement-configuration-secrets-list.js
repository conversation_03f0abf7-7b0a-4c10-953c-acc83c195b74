/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Navigation pane
  CONFIGURATION: 'Configuration',
  // Banner
  SECRET_PL: 'Secrets',
  SECRET_DESC:
    'Un secreto es un objeto que contiene una pequeña cantidad de datos confidenciales, como una contraseña, un token o una clave.',
  // List
  SECRET_FIELD_COUNT: 'Los campos',
  SECRET_EMPTY_DESC:
    'Un secreto es un objeto que contiene una pequeña cantidad de datos confidenciales, como una contraseña, un token o una clave.',
  // List > Create > Basic Information
  SECRET: 'Secreto',
  // List > Create > Data Settings
  DATA_SETTINGS: 'Data Settings',
  IMAGE_REGISTRY_INFORMATION: 'Secreto del repositorio de imágenes',
  TLS_INFORMATION: 'TLS information',
  USERNAME_PASSWORD: 'Secreto de contraseña de cuenta',
  ADD_DATA_TCAP: 'Agregar datos',
  ADD_DATA_DESC: 'Agregar datos de par clave / valor',
  REGISTRY_ADDRESS_TIP: 'Establezca una dirección de registro, por ejemplo, docker.io.',
  IMAGE_REGISTRY_REQUIRED_DESC:
    'Establezca la dirección de registro, el nombre de usuario y la contraseña.',
  CREDENTIAL_NAME_EMPTY_DESC: 'Por favor introduce la credencial',
  ENTER_PRIVATE_KEY_DESC: 'Por favor introduce la clave privada',
  ENTER_DATA_DESC: 'Por favor introduce los datos',
  PRIVATE_KEY_TCAP: 'Clave privada',
  REGISTRY_ADDRESS_TCAP: 'Dirección de registro',
  REGISTRY_SECRET_VER_ERR: 'La verificación del registro falló',
  REGISTRY_SECRET_VER_SUC: 'Verificación de registro exitosa',
  SECRET_NO_CHINESE_CODE_DESC:
    'La clave del Secreto debe constar de caracteres alfanuméricos, guiones (-), guiones bajos (_) o puntos (.).',
  SECRET_TYPE_DESC: 'Puedes elegir o personalizar un tipo secreto.',
  IMAGE_REGISTRY_VALIDATE_TIP:
    'Valide el nombre de usuario y la contraseña antes de crear el registro de imágenes Secreto.',
  DATA_KEY: 'Clave',
  DATA_VALUE: 'Valor',
  DEFAULT: 'Predeterminado',
  USERNAME_AND_PASSWORD: 'Secreto de contraseña de cuenta',
  // List > Edit Information
  // List > Edit YAML
  // List > Edit Settings
  DATA: 'Datos',
  EDIT_DATA_TCAP: 'Editar datos',
};
