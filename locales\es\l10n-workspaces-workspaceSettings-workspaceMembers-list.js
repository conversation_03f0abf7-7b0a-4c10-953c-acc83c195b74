/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Navigation Pane
  WORKSPACE_MEMBERS: 'Workspace Members',
  // Banner
  WORKSPACE_MEMBER_PL: 'Workspace Members',
  WORKSPACE_MEMBER_DESC:
    'Workspace members can view or manage workspace resources. You can manage members and control their permissions in the workspace.',
  // List
  WORKSPACE_MEMBER_EMPTY_DESC: 'Please invite a user to the workspace.',
  ROLE: 'Role',
  REMOVE_MEMBER: 'Eliminar miembro',
  REMOVE_MULTIPLE_MEMBERS: 'Remove Multiple Members',
  REMOVE_MULTIPLE_MEMBERS_TIP:
    'Enter the usernames <strong>{resource}</strong> to confirm that you understand the risks of this operation.',
  // List > Invite
  INVITE_WORKSPACE_MEMBER_DESC: 'You can invite members to the workspace.',
  INVITE_MEMBER: 'Invite Member',
  INVITE: 'Invite',
  INVITED_SUCCESSFULLY: 'Invited successfully.',
  // List > Remove
  REMOVE_MEMBER_TIP:
    '¿Estás seguro de que deseas eliminar el miembro <strong>{resource}</strong> ?',
};
