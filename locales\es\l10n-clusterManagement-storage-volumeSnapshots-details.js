/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // More > Create Volume
  CREATE_VOLUME: 'Create Volume',
  SNAPSHOT_CLASS_NOT_EXIST_TITLE: 'Volume Snapshot Class Not Found',
  SNAPSHOT_CLASS_NOT_EXIST: 'The volume snapshot class of the volume snapshot does not exist.',
  // Attributes
  // Data Source
  STORAGE_CLASS_SCAP: 'Storage class',
  DATA_SOURCE: 'Fuente de datos',
};
