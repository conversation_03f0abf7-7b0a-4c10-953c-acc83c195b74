on:
  push:
    # Sequence of patterns matched against refs/tags
    tags:
      - 'v*' # Push events to matching v*, i.e. v1.0, v20.15.10
name: Build Release

jobs:
  release:
    runs-on: ubuntu-latest
    if: github.repository	== 'kubesphere/console'
    steps:
      - uses: actions/checkout@v2

      - name: Login to HUAWEICLOUD
        uses: docker/login-action@v3
        with:
          registry: swr.cn-southwest-2.myhuaweicloud.com
          username: ${{ secrets.HUAWEICLOUD_USERNAME }}
          password: ${{ secrets.HUAWEICLOUD_PASSWORD }}

      - name: Login to DOCKER
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: all

      - name: Set up Docker buildx
        uses: docker/setup-buildx-action@v3

      - name: Chose Registry by tag
        id: chose_registry
        run: |
          if [[ ${GITHUB_REF#refs/*/} =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
              echo "env=prod" >> "$GITHUB_OUTPUT"
          else
              echo "env=dev" >> "$GITHUB_OUTPUT"
          fi
          echo "tag=${GITHUB_REF#refs/*/}" >> "$GITHUB_OUTPUT"

      - name: Generate output dir
        run: |
          make container-cross

      - name: Build and push ks-console images
        uses: docker/build-push-action@v6
        if: steps.chose_registry.outputs.env == 'prod'
        with:
          context: ${{ github.workspace }}
          file: build/Dockerfile.dapper
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            docker.io/kubesphere/ks-console:${{ steps.chose_registry.outputs.tag }}

      - name: Build and push ks-console dev images
        uses: docker/build-push-action@v6
        if: steps.chose_registry.outputs.env == 'dev'
        with:
          context: ${{ github.workspace }}
          file: build/Dockerfile.dapper
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            docker.io/kubespheredev/ks-console:${{ steps.chose_registry.outputs.tag }}

      - name: Sync ks images to HUAWEICLOUD
        if: steps.chose_registry.outputs.env == 'prod'
        run: |
          # console
          docker pull docker.io/kubesphere/ks-console:${{ steps.chose_registry.outputs.tag }}
          docker tag docker.io/kubesphere/ks-console:${{ steps.chose_registry.outputs.tag }} swr.cn-southwest-2.myhuaweicloud.com/ks/kubesphere/ks-console:${{ steps.chose_registry.outputs.tag }}
          docker push swr.cn-southwest-2.myhuaweicloud.com/ks/kubesphere/ks-console:${{ steps.chose_registry.outputs.tag }}
