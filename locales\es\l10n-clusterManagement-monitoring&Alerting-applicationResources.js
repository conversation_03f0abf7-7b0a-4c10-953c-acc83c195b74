/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  APPLICATION_RESOURCE_PL: 'Monitorización de recursos de aplicaciones',
  MONITORING_APPLICATION_DESC:
    'Application resources provide the monitoring data of application resource usage and usage ranking.',
  // Resource Usage > Cluster Resource Usage
  CUSTOM_TIME_RANGE: 'Customize Time Range',
  ACCOUNTS: 'Accounts',
  DEVOPS_PROJECT_PL: 'DevOps Projects',
  SAMPLING_INTERVAL: 'Sampling Interval',
  KUBE_SUN: 'Dom',
  KUBE_MON: 'Lun',
  KUBE_TUE: 'Mar',
  KUBE_WED: 'Miér',
  KUBE_THU: 'Juev',
  KUBE_FRI: 'Vier',
  KUBE_SAT: 'Sáb',
  KUBE_SUNDAY: 'Domingo',
  KUBE_MONDAY: 'Lunes',
  KUBE_TUESDAY: '<PERSON><PERSON>',
  KUBE_WEDNESDAY: 'Mi<PERSON>rc<PERSON><PERSON>',
  <PERSON><PERSON><PERSON>_THURSDAY: 'Jueves',
  KUBE_FRIDAY: 'Viernes',
  KUBE_SATURDAY: 'Sábado',
  KUBE_JAN: 'Ene',
  KUBE_FEB: 'Feb',
  KUBE_MAR: 'Mar',
  KUBE_APR: 'Abr',
  KUBE_MAY: 'May',
  KUBE_JUN: 'Jun',
  KUBE_JUL: 'Jul',
  KUBE_AUG: 'Ago',
  KUBE_SEP: 'Sep',
  KUBE_OCT: 'Oct',
  KUBE_NOV: 'Nov',
  KUBE_DEC: 'Dic',
  KUBE_JAN_LH: 'Enero',
  KUBE_FEB_LH: 'Febrero',
  KUBE_MAR_LH: 'Marzo',
  KUBE_APR_LH: 'Abril',
  KUBE_MAY_LH: 'Mayo',
  KUBE_JUN_LH: 'Junio',
  KUBE_JUL_LH: 'Julio',
  KUBE_AUG_LH: 'Agosto',
  KUBE_SEP_LH: 'Septiembre',
  KUBE_OCT_LH: 'Octubre',
  KUBE_NOV_LH: 'Noviembre',
  KUBE_DEC_LH: 'Diciembre',
  // Resource Usage > Application Resource Usage
  APPLICATION_RESOURCE_USAGE: 'Uso de recursos de la aplicación',
  DEPLOYMENT: 'Deployment',
  DEPLOYMENT_PL: 'Deployments',
  DEPLOYMENT_LOW: 'deployment',
  STATEFULSET: 'StatefulSet',
  STATEFULSET_PL: 'StatefulSets',
  STATEFULSET_LOW: 'statefulset',
  DAEMONSET: 'DaemonSet',
  DAEMONSET_PL: 'DaemonSets',
  DAEMONSET_LOW: 'daemonset',
  DEPLOYMENTS_VALUE: 'Deployments: {value}',
  STATEFULSETS_VALUE: 'StatefulSets: {value}',
  DAEMONSETS_VALUE: 'DaemonSets: {value}',
  RUNNING_PODS: 'Pods en ejecución',
  ROUTE: 'Ruta',
  // Resource Usage > Projects
  PROJECT_PL: 'Projects',
  PROJECT_COUNT: 'Projects',
  // Usage Ranking
  USAGE_RANKING: 'Usage Ranking',
  QUOTA_VALUE: 'Quota: {value}',
  OUTBOUND_TRAFFIC: 'Outbound Traffic',
  INBOUND_TRAFFIC: 'Inbound Traffic',
  SORT_BY_NAMESPACE_CPU_USAGE: 'Ordenar por uso de CPU',
  SORT_BY_NAMESPACE_POD_COUNT: 'Sort by pod quota usage (%)',
  SORT_BY_NAMESPACE_NET_BYTES_RECEIVED: 'Ordenar por tráfico entrante',
  SORT_BY_NAMESPACE_NET_BYTES_TRANSMITTED: 'Ordenar por tráfico saliente',
};
