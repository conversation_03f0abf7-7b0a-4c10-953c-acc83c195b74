/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  SERVICE_ROUTING: 'Service routing',
  SERVICE_ROUTING_LOW: 'service routing',
  SERVICE_ROUTING_PL: 'Service Routing',
  SERVICE_ROUTING_DESC:
    'As the entrance of the background architecture, the microservice gateway provides functions such as routing and forwarding, API management, and access filters, and is an important component in the microservice architecture. ',
  WHAT_IS_SERVICE_ROUTING_Q: 'What is service routing? ',
  WHAT_IS_SERVICE_ROUTING_A: '',
  // List
  SERVICE_ROUTING_EMPTY_DESC: 'You need to create a microservice gateway before creating a route. ',
  // List -> Create
  CREATE_SERVICE_ROUTING: 'Create service route',
  WEIGHT_DESC: 'Evaluate rules from lowest order to highest order. ',
  SERVICE_ROUTING_URL_DESC: 'Uniform resource identifier. ',
  PREDICATE: 'Predicate',
  SERVICE_ROUTING_FILTER: 'Filter',
};
