/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Title
  // Search Bar
  // Time Topology
  STOP_REAL_TIME_RESOURCE_EVENT: 'Pause real-time resource events',
  START_REAL_TIME_RESOURCE_EVENT: 'View real-time resource events',
  // Time Topology > Histogram
  RESOURCE_EVENT_COUNT: '資源事件',
  // List
  // List > Resource Event Details > Metadata
  METADATA: 'Metadata',
  // List > Resource Event Details > Details
  EARLIEST_START_TIME: 'Earliest Start Time',
  LATEST_START_TIME: 'Latest Start Time',
};
