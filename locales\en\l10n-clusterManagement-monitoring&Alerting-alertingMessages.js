/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  ALERTING_MESSAGE_PL: 'Alerts',
  ALERT_MESSAGE_DESC:
    'Alerts are generated when resource metrics meet conditions configured in rule groups.',
  // Custom > List
  ALERTS_FROM_CUSTOM_POLICIES: 'From Custom Rules',
  ALERTS_FROM_BUILT_IN_POLICIES: 'From Built-in Rules',
  TRIGGER_TIME: 'Trigger Time',
  ALERTING_MESSAGE_EMPTY_DESC: 'No alert is found in the current project.',
  MONITORING_TARGET: 'Monitoring Target',
  // Built-in > List
};
