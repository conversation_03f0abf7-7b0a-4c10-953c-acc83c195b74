/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  // More > Edit Settings
  // More > Edit Settings > Update Strategy
  // More > Edit Settings > Containers
  // More > Edit Settings > Volumes
  // More > Edit Settings > Pod Scheduling Rules
  // More > Edit Settings > Cluster Differences
  // More > Edit YAML
  // More > Delete
  // Monitoring
  // Config
  GATEWAY_CONFIGURATION: 'Gateway Configuration',
  // Resource Status
  MICROSERVICEGATEWAYS_REPLICA_DESC:
    'Deployment is used to describe a desired state that is expected to be reached by the application. It is mainly used to describe stateless applications. The number and state of replicas are maintained by the deployment controller, ensuring the state is consistent with the defined expected state. You can increase the replicas to meet higher loads. Rolling back the deployment version can eliminate program bugs. And you can create an autoscaler to flexibly handle the load in different scenarios.',
};
