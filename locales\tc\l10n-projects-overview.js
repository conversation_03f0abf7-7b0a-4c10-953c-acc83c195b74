/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Edit Quotas
  EDIT_QUOTAS: '編輯配額',
  QUOTA: 'Quota',
  PROJECT_QUOTAS_NOT_SET: '項目配額未設置',
  PROJECT_QUOTAS_DESC:
    'Project quotas specify the number of available CPU and memory resources and the maximum number of application resources such as Pods, Deployments, and Services in the project.',
  DEFAULT_CONTAINER_QUOTAS_NOT_SET: '容器資源預設請求未設置',
  DEFAULT_CONTAINER_QUOTAS_DESC:
    'Default container quotas specify the default CPU request, CPU limit, memory request, and memory limit of containers created in the project.',
  APPLICATION_RESOURCE_COUNT: '應用資源監控',
  SELECT_RESOURCE_TIP: 'Select a resource or enter a resource name',
  NUMBER_OF_PODS: 'Number of pods',
  NUMBER_OF_DEPLOYMENTS: 'Number of deployments',
  NUMBER_OF_STATEFULSETS: 'Number of statefulsets',
  NUMBER_OF_DAEMONSETS: 'Number of daemonsets',
  NUMBER_OF_JOBS: 'Number of jobs',
  NUMBER_OF_CRONJOBS: 'Number of cronjobs',
  NUMBER_OF_VOLUMES: 'Number of persistent volume claims',
  NUMBER_OF_SERVICES: 'Number of services',
  NUMBER_OF_ROUTES: 'Number of routes',
  NUMBER_OF_SECRETS: 'Number of secrets',
  NUMBER_OF_CONFIGMAPS: 'Number of configmaps',
  // Deployed Apps
  INSTALLED_APPS: 'Installed Apps',
  // Resource Status
  RESOURCE_STATUS: '資源狀態',
  // Resource Status > Application Resources
  RESOURCE_WARNING_TIPS: '有 {warnNum} 個 {tipName} 狀態異常',
  // Resource Status > Physical Resources
  PHYSICAL_RESOURCE_PL: 'Physical Resources',
  CPU_USAGE_TIME: 'CPU 使用量（{time}）',
  MEMORY_USAGE_TIME: '記憶體使用量（{time}）',
  // Tips
  TIPS: 'Tips',
  HOW_TO_INVITE_USERS: '邀請其他成員到目前項目中?',
  HOW_TO_SET_PROJECT_GATEWAY: '如何設置項目網關？',
  // Top 5 for Resource Usage
  TOP_5_FOR_RSC_USAGE: '資源用量 Top 5',
  TOP_5_FOR_RESOURCE_USAGE: '資源用量 Top 5',
  SORT_BY_WORKLOAD_CPU_USAGE: '按 CPU 使用量排行',
  SORT_BY_WORKLOAD_MEMORY_USAGE_WO_CACHE: '按記憶體使用量排行',
  SORT_BY_WORKLOAD_NET_BYTES_TRANSMITTED: '按網路流出速率排行',
  SORT_BY_WORKLOAD_NET_BYTES_RECEIVED: '按網路流入速率排行',
};
