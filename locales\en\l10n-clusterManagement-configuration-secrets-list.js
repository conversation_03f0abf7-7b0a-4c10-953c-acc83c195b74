/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Navigation pane
  CONFIGURATION: 'Configuration',
  // Banner
  SECRET_PL: 'Secrets',
  SECRET_DESC:
    'A secret is an object that contains a small amount of sensitive data such as a password, a token, or a key.',
  // List
  SECRET_FIELD_COUNT: 'Fields',
  SECRET_EMPTY_DESC: 'Please create a secret.',
  // List > Create > Basic Information
  SECRET: 'Secret',
  // List > Create > Data Settings
  DATA_SETTINGS: 'Data Settings',
  IMAGE_REGISTRY_INFORMATION: 'Image registry information',
  TLS_INFORMATION: 'TLS information',
  USERNAME_PASSWORD: 'Username and password',
  ADD_DATA_TCAP: 'Add Data',
  ADD_DATA_DESC: 'Add a key-value pair.',
  REGISTRY_ADDRESS_TIP: 'Set a registry address, for example, docker.io.',
  IMAGE_REGISTRY_REQUIRED_DESC: 'Please set the registry address, username, and password.',
  CREDENTIAL_NAME_EMPTY_DESC: 'Please enter a credential name.',
  ENTER_PRIVATE_KEY_DESC: 'Please enter a private key.',
  ENTER_DATA_DESC: 'Please add data.',
  PRIVATE_KEY_TCAP: 'Private Key',
  REGISTRY_ADDRESS_TCAP: 'Registry Address',
  REGISTRY_SECRET_VER_ERR: 'Registry verification failed.',
  REGISTRY_SECRET_VER_SUC: 'The registry is available.',
  SECRET_NO_CHINESE_CODE_DESC:
    'The key of the secret must consist of alphanumeric characters, hyphens (-), underscores (_), or periods (.).',
  SECRET_TYPE_DESC: 'Select a secret type.',
  IMAGE_REGISTRY_VALIDATE_TIP:
    'Please validate the username and password before creating the image registry secret.',
  DATA_KEY: 'Key',
  DATA_VALUE: 'Value',
  DEFAULT: 'Default',
  USERNAME_AND_PASSWORD: 'Username and password',
  // List > Edit Information
  // List > Edit YAML
  // List > Edit Settings
  DATA: 'Data',
  EDIT_DATA_TCAP: 'Edit Data',
  // List > Delete
};
