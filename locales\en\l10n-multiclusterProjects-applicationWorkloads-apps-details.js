/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // More > Add Component
  // More > Add Component > Create Service
  // More > Delete
  // Attributes
  // Resource Status
  APPLICATION_GOVERNANCE_SCAP: 'Application governance',
  NO_SERVICE_FOUND: 'No Service Found',
  // External Access
  GATEWAY_IP_ADDRESS: 'Gateway IP address',
  GATEWAY_ACCESS_MODE: 'Gateway access mode',
  NODE_PORTS_SCAP: 'Node ports',
  LOAD_BALANCER_SCAP: 'Load balancer',
  LOAD_BALANCERS_SCAP: 'Load balancers',
  // Traffic Management
  TRAFFIC_MANAGEMENT: 'Traffic Management',
  // Tracing
  APPLICATION_GOVERNANCE_ENABLED: 'Application governance enabled',
  APPLICATION_GOVERNANCE_DISABLED: 'Application governance disabled',
};
