/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  NUMBER_OF_PODS: 'Number of Pods',
  STRATEGY_PL: 'Strategy',
  // Cluster Node List
  CORE: 'Core',
  About: 'About',
  'Access Control': 'Access Control',
  Account: 'Account',
  active: 'Active',
  Active: 'Active',
  ACTIVITY: 'Activity',
  VIEW_RECORDS: 'View Records',
  Add: 'Add',
  'Add ': 'Add ',
  'Add Param': 'Add Param',
  ADD_PARAMETER: 'Add Parameter',
  'Add Task': 'Add Task',
  'Advanced Settings': 'Advanced Settings',
  Agent: 'Agent',
  All: 'All',
  'All Repositories': 'All Repositories',
  'Analysis Tools': 'Analysis Tools',
  'API Documents': 'API Documents',
  'App Templates': 'App Templates',
  Application: 'Application',
  APPLICATION: 'Application',
  APPLICATION_WORKLOADS: 'Application Workloads',
  Apply: 'Apply',
  auditing: 'Auditing',
  Auditing: 'Auditing',
  'Auditing Operating': 'Auditing Operating',
  'Back To List': 'Back To List',
  'Basic Info': 'Basic Info',
  'Basic Information': 'Basic Information',
  body: 'Body',
  Cancel: 'Cancel',
  'Canceled Successfully': 'Canceled Successfully',
  Ceased: 'Ceased',
  'clear search conditions': 'Clear search conditions.',
  CLUSTER_COUNT: 'Cluster Count',
  Collapse: 'Collapse',
  Completed: 'Completed',
  Configuration: 'Configuration',
  Confirm: 'Confirm',
  Container: 'Container',
  Containers: 'Containers',
  Contributors: 'Contributors',
  'Control Tools': 'Control Tools',
  Cordon: 'Cordon',
  Count: 'Count',
  Create: 'Create',
  Current: 'Current',
  Custom: 'Custom',
  'Data Sources': 'Data Sources: Built-in Services',
  Days: 'Days',
  deleted: 'Deleted',
  Deleted: 'Deleted',
  Destroy: 'Destroy',
  Detail: 'Detail',
  'Developer Community': 'Developer Community',
  Disabled: 'Disabled',
  disabled: 'Disabled',
  Display: 'Display',
  'Display all': 'Display all',
  'Display selected': 'Display selected',
  'Do HH:mm': 'Do HH:mm',
  domain: 'Domain',
  Domain: 'Domain',
  Download: 'Download',
  Downstream: 'Downstream',
  Drain: 'Drain',
  Draining: 'Draining',
  KEY_EXIST_DESC: 'Key already exists.',
  'Edit ': 'Edit ',
  EDIT_MODE: 'Edit Mode',
  'eg.': 'e.g.:',
  email: 'Email',
  EMPTY_KEY_DESC: 'Please enter a key.',
  Enabled: 'Enabled',
  'End Time': 'End Time',
  Error: 'Error',
  'Error Tips': 'Error Tips',
  event: 'Event',
  'Event metadata': 'Event Metadata',
  EVENT_SEARCH: 'Event Search',
  EVENT_DESC:
    'Event search (Event) is a historical event query for platform resources provided by KubeSphere',
  Events: 'Events',
  events: 'Events',
  exists: 'Exists',
  Failed: 'Failed',
  failed: 'Failed',
  False: 'False',
  false: 'False',
  filename: 'Filename',
  'half month': 'Half Month',
  'half month ago': 'Half Month Ago',
  Healthy: 'Healthy',
  healthy: 'Healthy',
  Hidden: 'Hidden',
  History: 'History',
  Hours: 'Hours',
  Images: 'Images',
  Inbound: 'Inbound',
  Infrastructure: 'Infrastructure',
  'is updating': 'Is Updating',
  key: 'Key',
  Language: 'Language',
  'Learn more': 'Learn More',
  'Remaining Quota': 'Remaining Quota',
  level: 'Level',
  'Load more': 'Load More',
  'Log Out': 'Log Out',
  LOG_SEARCH: 'Log Search',
  logging: 'Logging',
  memory: 'Memory',
  message: 'Message',
  MicroSeconds: 'MicroSeconds',
  MilliSeconds: 'MilliSeconds',
  Minutes: 'Minutes',
  'MMMM Do YYYY': 'MMMM Do YYYY',
  Monitors: 'Monitors',
  More: 'More',
  name: 'Name',
  NanoSeconds: 'NanoSeconds',
  New: 'New',
  'No Available Resource': 'No Available Resource',
  No: 'No',
  'No Data': 'No Data',
  NO_PARAMETERS: 'No Parameters Found',
  'No Relevant Data': 'No Relevant Data',
  NO_RELEVANT_DATA: 'No Relevant Data Found',
  'No matching resources found.': 'No Matching Resources Found.',
  'Node Selector': 'Node Selector',
  'Not Enabled': 'Not Enabled',
  'Number of events': 'Number of Events',
  'Official Document': 'Official Document',
  'Operation Account': 'Operation Account',
  OPERATION_DETAILS: 'Operation Details',
  Optional: 'Optional',
  or: 'Or',
  Others: 'Others',
  OUT: 'Out',
  Outbound: 'Outbound',
  Overview: 'Overview',
  'Params Configuration': 'Params Configuration',
  password: 'Password',
  Path: 'Path',
  Password: 'Password',
  Pause: 'Pause',
  Pending: 'Pending',
  pending: 'Pending',
  Platform: 'Platform Management',
  platform: 'Platform',
  'Platform Status': 'Platform Status',
  'Platform Title': 'Platform Title',
  'Platform URL': 'Platform URL',
  'Platform Version': 'Platform Version',
  PLATFORM_LEVEL_ACCESS_CONTROL: 'Platform Level Access Control',
  'Filter by keyword': 'Filter by Keyword',
  'Search by keyword': 'Search by Keyword',
  'Please input value': 'Please Input Value',
  'Please input password': 'Please Enter the Password.',
  'Please select project': 'Please Select Project',
  'Please select workspace': 'Please Select Workspace',
  'Pod Template': 'Pod Template',
  Pods: 'Pods',
  Preferences: 'Preferences Settings',
  'Quick Support': 'Quick Support',
  ReadAndWrite: 'Read and Write',
  ReadOnly: 'Read Only',
  Read: 'Read',
  Ready: 'Ready',
  reason: 'Reason',
  'Recent Visit': 'Recent Visit',
  Reason: 'Reason',
  Record: 'Record',
  Records: 'Records',
  Refresh: 'Refresh',
  refresh: 'Refresh',
  'refresh data': 'Refresh Data',
  'Related resources': 'Related Resources',
  Remain: 'Remain',
  REMOVE_USER_TIP: 'Are you sure to remove user <strong>{username}</strong>?',
  RESET: 'Reset',
  'Resource List': 'Resource List',
  Resource: 'Resource',
  Restart: 'Restart',
  Running: 'Running',
  Scheme: 'Scheme',
  'Search By Condition': 'Search By Condition',
  'search condition': 'Search Condition',
  'Search tips': 'Search Tips',
  Seconds: 'Seconds',
  secret: 'Secret',
  secret_text: 'Secret Text',
  Service: 'Service',
  NUMBER_OF_SERVICES: 'Number of Services',
  'Service Components': 'Service Components',
  'Session timeout or this account is logged in elsewhere, please login again':
    'Session timeout or this account is logged in elsewhere, please login again',
  Set: 'Set',
  IN_PROGRESS: 'In Progress',
  Settings: 'Settings',
  Source: 'Source',
  'Source IP': 'Source IP',
  status: 'Status',
  'Status Code': 'Status Code',
  stopped: 'Stopped',
  Stopped: 'Stopped',
  Strategy: 'Strategy',
  Subresource: 'Subresource',
  Succeeded: 'Succeeded',
  Success: 'Success',
  Successful: 'Successful',
  successful: 'Successful',
  'Sure to delete': 'Sure to Delete',
  'Sure to remove': 'Sure to Remove',
  Suspend: 'Suspend',
  Tag: 'Tag',
  Task: 'Task',
  terminated: 'Terminated',
  time: 'Time',
  To: 'To',
  to: 'To',
  Total: 'Total',
  total: 'Total',
  'Cluster List': 'Cluster List',
  true: 'True',
  True: 'True',
  type: 'Type',
  Uncordon: 'Uncordon',
  Undo: 'Undo',
  Unfinished: 'Unfinished',
  unfinished: 'Unfinished',
  Unit: 'Unit',
  unit: 'Unit',
  unknown: 'Unknown',
  Unknown: 'Unknown',
  'Unknown User': 'Unknown User',
  Unresolved: 'Unresolved',
  Update: 'Update',
  'Updated at': 'Updated At',
  'Updated just now': 'Just Updated',
  updating: 'Updating',
  Updating: 'Updating',
  Upgrading: 'Upgrading',
  Upstream: 'Upstream',
  Used: 'Used',
  'User Guides': 'User Guides',
  'User Manual': 'User Manual',
  'User Settings': 'User Settings',
  Username: 'Username',
  Value: 'Value',
  value: 'Value',
  verb: 'Verb',
  'Version Info': 'Version Information',
  View: 'View',
  Visit: 'Visit',
  'Volume Settings': 'Volume Settings',
  'Volume Snapshot': 'Volume Snapshot',
  VolumeSnapshot: 'Volume Snapshot',
  waiting: 'Waiting',
  WAITING: 'Waiting',
  warning: 'Warning',
  Warning: 'Warning',
  Workbench: 'Workbench',
  'workspaces found': 'Workspaces Found: {count}',
  'You can try to': 'You Can Try To',
  'YYYY-MM-DD': 'YYYY-MM-DD',
  _all: 'All Projects',
  NUM_UNIT: '',
  NAME_TOO_LONG: 'Up to 63 characters',
  LONG_NAME_TOO_LONG: 'Up to 253 characters',
  DELETE_DESC:
    'Are you sure you want to delete this resource? Once deleted, it cannot be recovered.',
  STOP_DESC: 'Are you sure you want to stop this resource?',
  DELETE_RESOURCE_TYPE_DESC:
    'Please enter the name of the {type} <strong>{resource}</strong> to confirm you understand the risks of this operation.',
  DELETE_APP_RESOURCE_TIP:
    'The resource is managed by application <strong>{app}</strong>, deletion may affect the normal use of this application. Please enter the name of the {type} <strong>{resource}</strong> to ensure you understand the risks involved.',
  STOP_APP_RESOURCE_TIP:
    'The resource is managed by application <strong>{app}</strong>, stopping it may affect the normal use of this application. Please enter the name of the {type} <strong>{resource}</strong> to ensure you understand the risks involved.',
  DELETE_RESOURCE_TITLE: 'Warning',
  DELETE_RESOURCE_TIP:
    'Are you sure about deleting the resource <strong>{resource}</strong>? The resource cannot be recovered after it is deleted.',
  add_: 'Add',
  REMOVE_GROUP_TIP: 'Are you sure to remove the group <strong>{resource}</strong>?',
  UPDATE_TIME: 'Updated {diff}',
  MONTH_AGO: '{count} Month(s) Ago',
  MONTH_TIME: '{count} Month(s)',
  WEEK_AGO: '{count} Week(s) Ago',
  WEEK_TIME: '{count} Week(s)',
  DAY_AGO: '{count} Day(s) Ago',
  DAY_TIME: '{count} Day(s)',
  HOUR_AGO: '{count} Hour(s) Ago',
  HOUR_TIME: '{count} Hour(s)',
  MINUTE_AGO: '{count} Minute(s) Ago',
  MINUTE_TIME: '{count} Minute(s)',
  SECOND_AGO: '{count} Second(s) Ago',
  SECOND_TIME: '{count} Second(s)',
  EVENT_NORMAL: 'Normal',
  EVENT_WARNING: 'Warning',
  QUOTA_LIMIT_TIP:
    'This setting refers to the Limits in Kubernetes resource management, which mainly limits the maximum amount of resources that each container can use.',
  DETAIL_NOT_FOUND_DESC:
    'Sorry, no related resources were found. Return to <a href="{link}">{title}</a>',
  CONDITION_STATUS_ANALYSE: 'Condition Status Analysis',
  NAV_PROJECTS: 'Projects',
  NAV_ACCOUNTS: 'Account Management',
  'rules text': 'How to define operation auditing collection rules? 🤔',
  'rules desc':
    'KubeSphere can collect operation audits based on the rules you set. Click to see how to define rules.',
  EVENT_CREATE_DESC: 'You can change the search criteria to query again.',
  TOOLBOX_KUBECTL_DESC: 'Command line tool for operating multiple clusters',
  PLATFORM_SETTINGS_SELECTOR_DESC: 'Customize the system configuration of the platform',
  FILE_OVERSIZED_TIP: 'File too large, cannot upload files larger than 2M!',
  USER_GUIDES_DESC: 'The most comprehensive KubeSphere user guides',
  DEVELOPER_DESC: 'Get help by raising questions in the developer community',
  API_DOCS_DESC: 'The most comprehensive KubeSphere API documentation',
  GITHUB_ISSUES_DESC: 'Get help by raising issues on GitHub',
  // Workbench
  // Container Details Page
  CPU_VALUE: 'CPU: {value}',
  MEMORY_VALUE: 'Memory: {value}',
};
