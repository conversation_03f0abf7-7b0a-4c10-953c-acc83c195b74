/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  // Resource Status
  DOMAIN_NAME_VALUE: 'Domain Name: {value}',
  PATH_VALUE: 'Ruta: {value}',
  RULES: 'Reglas',
  ROUTE_PATH_VALUE: 'Path: <strong>{value}</strong>',
  ROUTE_SERVICE_VALUE: 'Servicio: <strong>{value}</strong>',
  ROUTE_PORT_VALUE: 'Port: <strong>{value}</strong>',
  SERVICE_COLON: 'Service: ',
  ACCESS_SERVICE: 'Access Service',
  UNABLE_TO_ACCESS: 'Unable to access service',
  UNABLE_TO_ACCESS_TIP:
    'Make sure that domain name resolution policies have been configured in your DNS server or the hosts file of your client machine.',
  CERTIFICATE_VALUE: 'Certificate: {value}',
  ROUTE_PATH: 'Path',
  ROUTE_PORT: 'Port',
};
