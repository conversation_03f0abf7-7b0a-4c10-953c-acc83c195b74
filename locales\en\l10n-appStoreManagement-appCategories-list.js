/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  APP_CATEGORY_PL: 'App Categories',
  APP_CATEGORIES_DESC:
    'Manage categories of apps released to the App Store. You can create, edit, delete, and change app categories.',
  // All Categories
  ALL_CATEGORIES_VALUE: 'All Categories ({value})',
  APP_CATEGORY_EMPTY_DESC: 'No apps belong to the category.',
  // All Categories > Add
  ENTER_CATEGORY_NAME_TIP: 'Please enter a category name.',
  CATEGORY_NAME_DESC:
    'The name can contain any characters and the maximum length is 20 characters.',
  APP_CATEGORY_NAME_DESC:
    'The name must consist of lower case alphanumeric characters, (-) or (.) and must start and end with an alphanumeric character.',
  // All Categories > Eit
  // All Categories > Delete
  DELETE_CATEGORY_DESC: 'Are you sure you want to delete the category <b>{name}</b>?',
  // List

  // List > Change Category
  CHANGED_SUCCESSFULLY: 'Changed successfully.',
  CHANGE_CATEGORY: 'Change Category',
  CHANGE_CATEGORY_DESC:
    'The app category determines the classification of the app in the App Store.',
};
