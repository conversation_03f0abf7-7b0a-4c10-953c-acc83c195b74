/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Log in page
  INPUT_USERNAME_OR_EMAIL_TIP: 'Please enter your username or email',
  INPUT_EMAIL_TIP: 'Please enter your email',
  LOG_IN: 'Log In',
  LOG_IN_WITH_TITLE: 'Log in with {title}',
  LOG_OUT: 'Log Out',
  ABOUT: 'About',
  USERNAME_OR_EMAIL: 'Username or Email',
  WELCOME: 'Welcome',
  USERNAME_WITH_TITLE: '{title} Username',
  INTERNAL_SERVER_ERROR_TIP: 'An unepected error occurred. Please refresh your page.',
  TOO_MANY_FAILURES:
    'The number of consecutive login failures has exceeded the limit. Please try again later.',
  FAILED_TO_ACCESS_BACKEND: 'Failed to access the backend services.',
  FAILED_TO_ACCESS_API_SERVER: 'Failed to access the API server.',
  LOGIN_AGAIN_DESC:
    'The session has timed out or the user has logged in elsewhere. Please log in again.',
  // Change password upon first login
  CHANGE_PASSWORD_TIP: 'You are logging in for the first time. Please change your password.',
  SUBMIT: 'Submit',
  CHANGE_PASSWORD_LATER: 'Change Password Later',
  PASSWORD_MUST: 'Your password must:',
  PASSWORD_LETTER: 'Contain at least 1 uppercase letter and 1 lowercase letter.',
  PASSWORD_NUMBER: 'Contain at least 1 number.',
  PASSWORD_LENGTH: 'Contain at least 8 characters.',
  // Third-party authentication
  SET_EMAIL_AND_USERNAME: 'Set Email and Username',
  PASSWORD_CHARACTERS:
    'Contain at least 1 special character (~!@#$%^&*()-_=+\\|[{}];:\'",<.>/? or space).',
  PLEASE_CONFIRM_YOUR_ACCOUNT_INFO: 'Please confirm your account info',
  PLEASE_INPUT_USERNAME: 'Please input username',
};
