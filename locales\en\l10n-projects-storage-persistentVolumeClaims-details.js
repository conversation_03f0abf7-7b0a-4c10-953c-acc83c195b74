/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // More > Edit YAML
  // More > Clone
  CLONE_VOLUME: 'Clone Volume',
  CLONE: 'Clone',
  // More > Create Snapshot
  CREATE_SNAPSHOT: 'Create Snapshot',
  SNAPSHOT_EMPTY_TIP: 'Please select a volume snapshot class.',
  // More > Expand
  EXPAND_VOLUME: 'Expand Volume',
  EXPAND: 'Expand',
  // Attributes
  PROVISIONER: 'Provisioner',
  // Resource Status
  AVAILABLE_CAPACITY: 'Available capacity',
  POD_IP_ADDRESS_SCAP: 'Pod IP address',
  TOTAL_CAPACITY: 'Total capacity',
  // Metadata
  // Events
  // Snapshots
};
