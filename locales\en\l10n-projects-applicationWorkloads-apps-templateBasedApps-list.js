/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  TEMPLATE_BASED_APP_PL: 'Template-Based Apps',
  APPLICATIONS_DESC:
    'An app provides users with comprehensive business functions in one package. App templates in KubeSphere are built on the Helm packaging specification. They are delivered through a unified public or private Helm repository. An app is composed of one or more Kubernetes objects including workloads, services and ingresses.',
  APP_PL: 'Apps',
  APP_TYPES_Q: 'What app types does KubeSphere support?',
  APP_TYPES_A:
    'KubeSphere supports templated-based apps. Template-based apps are created from You can deploy apps from the KubeSphere App Store or an app template.',
  HOW_TO_USE_APP_GOVERN_Q: 'How do I use Application Governance?',
  HOW_TO_USE_APP_GOVERN_A: 'You can enable Application Governance when you create a composed app.',
  DEPLOY_SAMPLE_APP: 'Deploy Sample App',
  // List
  NO_TEMPLATE_BASED_APP_FOUND: 'No Template-Based App Found',
  TEMPLATE_BASED_APP_EMPTY_DESC:
    'Please create an app from the KubeSphere App Store or an app template.',
  APP: 'App',
  VERSION: 'Version',
  CREATING: 'Creating',
  UPGRADING: 'Created',
  DELETING: 'Deleting',
  // List > Create
  CREATE_APP: 'Create App',
  CREATE_APP_DESC: 'Create an app from the KubeSphere App Store or an app template.',
  FROM_APP_STORE: 'From App Store',
  FROM_APP_TEMPLATE: 'From App Template',
  FROM_APP_STORE_DESC: 'Create an app from the KubeSphere App Store.',
  // List > Create > From App Template
  SELECT_APP_REPOSITORY: 'Select app repository',
  CURRENT_WORKSPACE: 'Current workspace',
  FROM_APP_TEMPLATE_DESC:
    'Create an app from an app template in the current workspace or in a remote app repository.',
  APP_TEMPLATES_MODAL_DESC:
    'Select the current workspace or a remote app repository from the drop-down list to view the available app templates.',
  // List > Create > From App Template > App Information
  // List > Create > From App Template > Chart Files
  // List > Edit
  // List > Delete
  APP_LOW: 'app',
  APP_STATUS_WITH_SUFFIX: 'Application instance deployment {suffix}',
  APP_DEPLOY_STATUS: 'Application deployment status',
  APP_DEPLOY_LOGS: 'Application instance deployment logs',
  deployFailed: 'Deployment failed',
  DEPLOYFAILED: 'Deployment failed',
};
