/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  // More > Update Gateway
  // More > Delete
  GATEWAY_LOW: 'gateway',
  // Monitoring
  REQUEST_COUNT: 'Requests',
  NETWORK_TRAFFIC: 'Network Traffic',
  CONNECTION_COUNT: 'Connections',
  FAILED_REQUEST_COUNT: 'Failed Requests',
  AVERAGE_LATENCY: 'Average Latency',
  P_FIFTY_LATENCY: 'P50 Latency',
  P_NINETY_FIVE_LATENCY: 'P95 Latency',
  P_NINETY_NINE_LATENCY: 'P99 Latency',
  FOUR_XX_REQUEST_COUNT: '4XX Requests',
  FIVE_XX_REQUEST_COUNT: '5XX Requests',
  TOTAL_REQUESTS: 'Total Requests',
  SUCCESSFUL_REQUESTS: 'Successful Requests',
  // Configuration Options
  CONFIGURATION_OPTIONS: 'Configuration Options',
  // Gateway Logs
  GATEWAY_LOGS: 'Gateway Logs',
  LOGGING_DISABLED: 'Logging Disabled',
  REFRESH_INTERVAL_VALUE: 'Refresh interval: {value}s',
  EXPORT_LOGS: 'Export Logs',
  // Resource Status > Replica Status
  // Resource Status > Ports
  // Resource Status > Pods
  // Metadata
};
