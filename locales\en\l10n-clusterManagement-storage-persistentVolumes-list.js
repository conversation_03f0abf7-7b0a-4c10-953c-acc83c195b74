/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  PERSISTENT_VOLUME: 'Persistent Volume',
  PERSISTENT_VOLUME_PL: 'Persistent Volumes',
  PERSISTENT_VOLUME_LOW: 'persistent volume',
  // List
  PV_STATUS_AVAILABLE: 'Available',
  PV_STATUS_BOUND: 'Bound',
  PV_STATUS_RELEASED: 'Released',
  PV_STATUS_FAILED: 'Failed',
  RECYCLING_STRATEGY: 'Recycling Strategy',
  VOLUME_EMPTY_DESC: 'Please bind a persistent volume claim to a workload.',
  // List > Delete
};
