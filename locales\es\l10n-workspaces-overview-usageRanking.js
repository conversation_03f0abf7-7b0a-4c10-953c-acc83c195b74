/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // List
  SORT_BY_WORKSPACE_MEMORY_USAGE: 'Ordenar por uso de memoria (con cache)',
  SORT_BY_WORKSPACE_MEMORY_USAGE_WO_CACHE: 'Ordenar por uso de memoria (sin cache)',
  SORT_BY_WORKSPACE_POD_COUNT: 'Sort by pod quota usage (%)',
  SORT_BY_WORKSPACE_CPU_USAGE: 'Ordenar por uso de CPU',
  SORT_BY_WORKSPACE_NET_BYTES_TRANSMITTED: 'Ordenar por tráfico saliente',
  SORT_BY_WORKSPACE_NET_BYTES_RECEIVED: 'Ordenar por tráfico entrante',
};
