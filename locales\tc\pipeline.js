/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  abortPipeline: '檢查結果是否影響後續任務',
  'Absolute duration': '代碼塊所用的絕對時間',
  'Add another credential': '增加一個憑證',
  'Add conditions': '添加條件',
  'Add nesting conditions': '添加嵌套條件',
  'Add nesting steps': '添加嵌套步驟',
  'Add Parallel Stage': '添加並行階段',
  'Add Step': '添加步驟',
  archiveArtifacts: '保存成品',
  'Are you sure to close this pipeline Editor ?': '確定關閉流水線編輯？',
  artifacts: '成品',
  'Authentication Token': '身份驗證令牌',
  'Automatically generated by GitHub': '由 GitHub 自動生成',
  bcc: '密件副本',
  BEHAVIORAL_STRATEGY: '行為策略',
  blocker: '阻斷',
  BLOCKER: 'Blocker',
  NOTICE: 'Notice',
  Branches: '分支',
  'branch success': '分支成功',
  '@somebody to help review': '可以@某人來幫助審核',
  'Send messages by email': '可以通過郵件發送訊息',
  'Send messages in the build': '可以在構建中發送訊息',
  'Change Current Directory': '更改目前目錄',
  'Chinese is not allowed in the pipeline configuration': '配置中不允許含有中文',
  'Clean when aborted': '清理失敗不影響運行',
  'Clean Workspace': '清理企業空間',
  'Code Quality Check': '代碼質量檢查',
  COMMIT_ID: 'Commit ID',
  'Config File Path': '配置文件路徑',
  'config name': '配置名稱',
  configs: '配置',
  'Configuration error': '配置資訊錯誤',
  container: '指定容器',
  Continue: '繼續編輯',
  'credential Id': '憑證 ID',
  'Credential Id': '憑證 ID',
  credentialsId: '憑證 ID',
  critical: '嚴重',
  'Current branch name must match the input value': '目前分支名稱需符合輸入值',
  'defaultValue -1 means not to discard': '預設值 -1: 不會丢棄紀錄',
  'Delete all resources of the deployment file': '刪除部署文件所對應的所有資源',
  'Deploy resources to the Kubernetes cluster': '將資源部署到 kubernetes 集群',
  "Didn't pass": '未通過',
  dir: '切換目錄',
  Discard: '忽略',
  'Discover branches from repository': '發現存儲庫上的分支',
  DISCOVER_PR_FROM_FORKS: '以 Fork 倉庫中發現 PR',
  DISCOVER_PR_FROM_ORIGIN: '以原倉庫中發現 PR',
  'Discover pull requests from forks': '發現 Fork 儲存庫與目標儲存庫相同的 PR',
  'Discover pull requests from origin': '發現原始儲存庫與目標儲存庫相同的 PR',
  'Docker Container Registry Credentials': 'Docker 容器倉庫憑證',
  'Docker Registry URL': 'Docker 倉庫 URL',
  dockerCredentials: 'Docker 容器倉庫憑證',
  'Drag and drop tasks to sort': '可以拖放任務進行排序',
  Duration: '持續時間',
  echo: '打印訊息',
  'Edit Config': '編輯配置',
  'Edit Credential': '編輯憑證',
  'Enable Variable Substitution in Config': '在配置中啟用變量替換',
  enableConfigSubstitution: '啟用變量替換',
  'Enter an expression': '輸入一個表達式',
  'Environment name': '環境變量名稱',
  'Executes the code inside the block with a determined time out limit.':
    '使用確定的超時限制執行塊内的代碼.',
  expression: '表達式',
  Failure: '失敗',
  'For accessing GitHub': '用於獲取 GitHub',
  'get token': '獲取 Token',
  info: '提示',
  input: '審核',
  'Inspection results do not affect subsequent tasks': '檢查結果不影響後續任務',
  'instance failed to match at least one schema': '至少需要一個嵌套步驟',
  'Internal nested conditions only need to satisfy one': '内部嵌套的條件只需符合一個',
  'Internal nesting conditions must be matched': '内部嵌套的條件需全部滿足',
  'Jenkinsfile syntax error, message': 'Jenkinsfile 語法錯誤，訊息',
  'key File Variable': '私鑰變量',
  keyFileVariable: '私鑰變量',
  'Kubeconfig Variable': 'kubeconfig 變量',
  'Kubernetes Namespace for Secret': 'Kubernetes 密鑰命名空間',
  'Kubernetes Secrets': 'Kubernetes 密鑰',
  Line: '行',
  'Load credentials into environment variables': '加載憑證到還境變量',
  'Load the sonarqube configuration provided by Jenkins into the Pipeline.':
    '將 Jenkins 中的 sonarqube 配置加載到流水線中',
  mail: '郵件',
  major: '重要',
  minor: '次要',
  "Missing one or more required properties: 'name'": 'name 不能為空',
  'Negative prefix': '否定前缀',
  NO_BRANCH_FOUND_TIP: '未發現任何分支',
  'No need': '不需要',
  Normal: '通用',
  'Not Build': '未執行',
  'Not fail build': '失敗不影響運行',
  'not support edit nested stage': '暫不支持編輯嵌套階段',
  'passphrase Variable': '密碼變量',
  passphraseVariable: '密碼變量',
  'Password Variable': '密碼變量',
  passwordVariable: '密碼變量',
  PIPELINE_LOW: '流水線',
  'Pipeline Configuration': '編輯流水線',
  'Pipeline List': '流水線列表',
  'pipeline syntax error': '流水線語法錯誤',
  'Please add at least one step.': '至少添加一個步驟',
  'Please input images name': '請輸入鏡像名稱',
  'Please input the credential name.': '請輸入憑證名稱',
  'Press enter for the next': '按Enter鍵以進行下一步',
  'Print message': '打印訊息',
  'Pull code by Git': '通過 Git 拉取代碼',
  'Pull code by SVN': '通過 SVN 拉取代碼',
  Queue: '列隊中',
  Recipient: '收件人',
  'Registry Credentials': '倉庫憑證',
  remote: '倉庫地址',
  'Run Pipeline': '運行流水線',
  'Save Artifact': '保存成品',
  'Repo Scanned Successfully': 'Repo Scanned Successfully',
  WEBHOOK_PUSH: 'Webhook 推送',
  secretName: '秘鑰名稱',
  secretNamespace: '密鑰命名空間',
  SELECT_THIS_REPOSITORY: '選擇此倉庫',
  Sender: '寄件人',
  sh: 'shell 腳本',
  'Shell commands can be executed in the container': '可以在容器中執行 shell 命令',
  'Show Advanced Settings': '顯示高级設置',
  'show yaml editor': '用 YAML 編輯器編輯',
  'sonar is the default config name.': '預設配置名稱是 sonar',
  "Sorry, you don't have the permission to do this.": '抱歉，您沒有權限進行此操作',
  'Specify a container to add nested tasks to execute inside the container':
    '指定容器，可添加嵌套任務在容器内執行',
  'Start the follow-up task after the inspection': '檢查通過後開始後續任務',
  'Started By': '實施者',
  Subject: '主題',
  submitter: '審核者',
  submitterParameter: '提交參數',
  'Text Variable': '文本變量',
  'The conditions required to implement the current phase (optional).':
    '執行目前階段段所需要滿足的條件 （可選）',
  'The environment variable entered before running the pipeline is match the current value.':
    '運行流水線前輸入的環境變量與目前值一致',
  'The label on which to run the Pipeline or individual stage': '流水線或單個階段的標籤',
  'This name has been used.': '此名稱已被使用',
  'Time Used': '用時',
  Timeout: '超時',
  timeout: '超時',
  'Timeout after no activity in logs for this block': '此區塊代碼無紀錄輸出後開始計算超時',
  timer: '定時器',
  TRIGGER_REMOTE_BUILD: '觸發遠程構建（例如，使用腳本）',
  Unnamed: '未命名',
  'Use the following URL to remotely triggerworkbench the build': '使用以下 URL 遠程觸發構建',
  'User types that can trigger builds': '可以觸發構建的用戶類型',
  'username or group name, multiple values ​​used, separated':
    '用戶名或組名，多個值使用英文逗號分隔',
  'Username Variable': '用戶名變量',
  usernameVariable: '用戶名變量',
  waitForQualityGate: '代碼質量檢查(SonarQube)',
  Webhook: 'Webhook',
  withCredentials: '添加憑證',
  withSonarQubeEnv: 'Sonarqube 配置',
  'Wrong Token': 'Token 錯誤',
  'You can execute shell commands or windows batch commands in the build.':
    '可以在構建中執行 shell 命令或者 windows 的 batch 命令',
  PIPELINE_DESC: `流水線是一組可擴展的工具，可以通過組合它們來實現持續集成和持續交付。
    您可以在此頁面創建並管理流水線。`,
  username_password: '帳戶憑證',
  login_Plateform: '登入平台',
  CREDENTIALS_DESC: `憑證是包含了一些敏感數據的對象，如用戶名密碼，SSH 密鑰和 Token 等,
    用於在 Pipeline 運行時, 為拉取代碼、push/pull 鏡像、SSH 執行腳本等過程提供認證`,
  AUTHENTICATION_TOKEN_TIP: `如果需要通過訪問預定義 URL 開觸發構建，請啟用此選項。
    此功能的的一個典型用法是通過源代碼管理系統的鉤子腳本來進行觸發。
    您需要提供一個字符串形式的授權令牌，以便只有擁有授權令牌的人才能觸發遠程構建。`,
  CRON_TIP: `This field follows the cron syntax (slightly different).
    Specifically, each line contains 5 fields separated by tabs or spaces.
    minutes: the first few minutes of an hour (0-59)
    hours: the first few hours of the day (0-23)
    the days of the month: the first few days of a month (1-31 )
    Month: The first few days of the first few days (0-12), the first few days of the week (0-12),
    0 and 7 are Sundays. `,
  PIPELINES_BASEINFO_DESC: '請輸入流水線的基本資訊',
  PIPELINE_ADVANCE_SETTINGS_DESC: '\t為流水線配置複雜行為策略（可選）',
  CREDENTIALS_CREATE_DESC: '創建用於 DevOps 項目中的憑證',
  CHECKOUT_DESC: '拉取代碼，常用於拉取非 Git 代碼，例如 SVN 等等',
  PRIVATE_IMAGE_DESC: `To deploy from a private image repository,
    you need to create a mirrored repository and then pull the image. `,
  AUTHENTICATION_TOKEN_DESC: `使用以下 URL 遠程觸發構建：
    JENKINS_URL / job / JOB_NAME / build？token =TOKEN_NAME
    或者/ buildWithParameters？token = TOKEN_NAME 可選擇附加＆cause =原因提供將包含在紀錄的構建原因中的文本。`,
  PIPELINE_NO_CONFIG: '目前 Pipeline 中並沒有找到相關配置文件',
  EDIT_CREDENTIAL_DESC: '下列表單不會顯示原有憑證資訊，重新輸入會將其覆蓋。',
  pipeline_conditions: '條件',
  CODE_SMELL: '容易出錯',
  BUG: 'Bug',
  VULNERABILITY: '漏洞',
  SECURITY_HOTSPOT: '安全',
  PIPELINE_CREATE_DESC: '初始化完成後將開始 Pipeline 的任務執行',
  PIPELINE_QUEUED_DESC:
    '您需要等待 agent 啟動並執行流水線（注：如 agent 長時間沒有啟動請檢查 agent 配置和集群資源情況）',
  waitForQualityGate_desc:
    '代碼質量檢查標準來源於SonarQube的 Quality Gate (質量閥)，如果需要自定義檢查標準請前往 SonarQube 設置',
  REVIEW_DESC: '流水線運行至此任務將會暫停，審核後可選擇繼續或终止',
  INPUT_DESC: '流水線運行至此任務將會暫停，創建者和被@的人可以選擇繼續或終止流水線',
  LoadPrevData_Desc: '檢測到此流水線上次未編輯成功，是否加載上次數據繼續編輯？',
  withSonarQubeEnv_DESC: '快速的定位代碼中潛在的或者明顯的錯誤',
  waitForQualityGate_DESC: '在執行代碼分析後執行',
  script_DESC: '執行groovy腳本',
  pipeline_owner: 'DevOps 项目的所有者，可以進行 DevOps 项目的所有操作',
  pipeline_maintainer: 'DevOps 项目的主要維護者，可以進行項目内的憑證配置、Pipeline 配置等操作',
  pipeline_developer: 'DevOps 项目的開發者，可以進行 Pipeline 的觸發以及查看',
  pipeline_reporter: 'DevOps 项目的觀察者，可以查看 Pipeline 的運行情况',
  PATTERN_PIPELINE_NAME_VALID_NAME_TIP: '名稱不合法 （僅支持大小寫字母、數字、_、-）',
  GET_GITHUB_TOKEN_DESC: `用於獲取 GitHub 代碼倉庫
  <a
    class="float-right"
    href="https://github.com/settings/tokens/new?scopes=repo,read:user,user:email,write:repo_hook"
    target="_blank"
  >
    獲取 Token
  </a>`,
  PIPELINES_FOOTER_SEE_MORE: '前往分支詳情頁查看更多 →',
  JENKINS_LINS_ERROR: '第 {line} 行有語法錯誤',
  // Concatenated
  'Invalid credential ID': '憑證 ID 格式不合法',
  'Batch Run Fail': '批量運行失敗',
  'Run Start': '運行開始',
  'Choose a Pipeline Template': '選擇流水線模板',
  // Pipeline Creation Page
  DEVOPS_PROJECT_DESC: 'DevOps 项目用於對資源進行分組管理以及控製不同用戶的資源管理權限。',
};
