/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  // More > Update Gateway
  // More > Delete
  GATEWAY_LOW: 'Gateway',
  // Monitoring
  REQUEST_COUNT: 'Requests',
  NETWORK_TRAFFIC: 'Network Traffic',
  CONNECTION_COUNT: 'Connections',
  FAILED_REQUEST_COUNT: 'Request Error',
  AVERAGE_LATENCY: 'Average Latency',
  P_FIFTY_LATENCY: 'P50 Latency',
  P_NINETY_FIVE_LATENCY: 'P95 Latency',
  P_NINETY_NINE_LATENCY: 'P99 Latency',
  FOUR_XX_REQUEST_COUNT: '4xx error',
  FIVE_XX_REQUEST_COUNT: '5xx error',
  TOTAL_REQUESTS: 'Total Requests',
  SUCCESSFUL_REQUESTS: 'Request Success',
  // Configuration Options
  CONFIGURATION_OPTIONS: 'Gateway Config',
  // Gateway Logs
  GATEWAY_LOGS: 'Gateway Logs',
  LOGGING_DISABLED: 'Logging Disabled',
  REFRESH_INTERVAL_VALUE: 'Refresh interval: {value}s',
  EXPORT_LOGS: 'Exportar logs',
};
