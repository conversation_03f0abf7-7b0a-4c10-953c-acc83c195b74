/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  COPIED_SUCCESSFUL: 'Copied successfully.',
  BUILDER_IMAGE: 'Builder Image',
  PULL_POLICY: 'Pull Policy',
  REMOTE_TRIGGER: 'Remote Trigger',
  // Run
  S2I_UPDATE_WORKLOAD: 'Update workload after image building is successful',
  S2I_UPDATA_WORKLOAD_DESC:
    'After the image is rebuilt successfully, workloads that use the image will be updated.',
  // More > Edit Information
  // More > Edit YAML
  // More > Delete
  IMAGE_BUILDER: 'Image Builder',
  IMAGE_BUILDER_LOW: 'image builder',
  // Run Records
  JOB_RECORDS: 'Job Records',
  LAST_BUILD_ENVIRONMENT: 'Last Build Environment',
  BUILDER_IMAGE_SCAP: 'Builder image',
  FILE_SIZE: 'File size: {size}',
  DOWNLOAD_ARTIFACT: 'Download Artifact',
  ARTIFACT_FILE: 'Artifact File',
  LOG_MODULE_NOT_INSTALLED: 'The logging module is not installed.',
  LOADING_DOTS: 'Loading...',
  IMAGE_NAME_SCAP: 'Image name',
  IMAGE_SIZE_SCAP: 'Image size',
  IMAGE_NAME_BUILDING: 'Image: {name}/Building',
  IMAGE_NAME_FAILED: 'Image: {name}/Failed',
  IMAGE_NAME_SUCCESSFUL: 'Image: {name}/Successful',
  LAST_MESSAGE_SCAP: 'Last message',
  START_TIME_SCAP: 'Start time',
  // Resource Status
  SOURCE_URL: 'Source URL',
  NEW_TAG: 'New Tag',
  NEW_TAG_DESC: 'Enter the tag of the new image',
  // Image Artifacts
  IMAGE_ARTIFACTS: 'Image Artifacts',
  RELEASE_TIME_SCAP: 'Release time',
  PULL_COMMAND_SCAP: 'Pull command',
  // Environment Variables
  // Events
};
