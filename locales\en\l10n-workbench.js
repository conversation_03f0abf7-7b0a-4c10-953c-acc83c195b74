/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Head
  WORKBENCH: 'Workbench',
  DASHBOARD_TITLE: 'Hello {username}',
  LAST_LOGIN_TIME: 'Last login: ',
  USER_DASHBOARD_EMPTY_TITLE: 'You do not belong to any workspace.',
  USER_DASHBOARD_EMPTY_DESC:
    'Please contact a workspace administrator to invite you to join a workspace.',
  // Platform Information
  LAST_UPDATE_TIME: 'Last update time',
  PLATFORM_VERSION: 'Platform version',
  // Platform Resources
  PLATFORM_RESOURCES: 'Platform Resources',
  APP_TEMPLATE_SCAP_PL: 'App templates',
  // Recent Access
  RECENT_ACCESS: 'Recent Access',
  MULTI_CLUSTER_DEPLOYMENT: 'Multi-cluster Project',
  WORKBENCH_PROJECT: 'Project',
  WORKBENCH_WORKSPACE: 'Workspace',
  WOR<PERSON><PERSON><PERSON><PERSON>_DEVOPS: 'DevOps project',
  WOR<PERSON><PERSON><PERSON>H_MULTI_CLUSTER_PROJECT: 'Multi-cluster project',
  WORKBENCH_CLUSTER: 'Cluster',
  NOT_FOUND_DESC:
    'The resource was not found. The system will return to <a href="{link}">Workbench</a> in {time}s.',
  MULTI_CLUSTER_PROJECT_TIP: 'The project is deployed across multiple clusters.',
  NO_HISTORY_TITLE: 'No Quick Access Resources Found',
  NO_HISTORY_DESC: 'You can add platform resources.',
  ADD_TO_QUICK_ACCESS: 'Add to Quick Access',
  CANCEL_QUICK_ACCESS: 'Cancel Quick Access',
  WORKSPACES_MANAGEMENT_DESC: 'Share resources across clusters in workspaces.',
  PLATFORM_CLUSTER_DESC: 'Manage resources based on infrastructures.',
  USER_AND_ROLE_MANAGEMENT: 'Users and Roles',
  USER_AND_ROLE_MANAGEMENT_DESC: 'Manage users and roles on the platform.',
  EXTENSION_DESC: 'Quick Access Entry for KubeSphere Extensions',
  ALL_WORKSPACE: 'Workspace',
  ALL_WORKSPACE_PL: 'Workspaces',
  WORKBENCH_ALL_CLUSTER: 'Cluster',
  WORKBENCH_ALL_CLUSTER_PL: 'Clusters',
  ALL_USER: 'User',
  ALL_USER_PL: 'Users',
  INSTALLED_COMPONENT: 'Installed Extension',
  INSTALLED_COMPONENT_PL: 'Installed Extensions',
  VISIBILITY_WORKSPACE: 'Visible workspace',
  VISIBILITY_WORKSPACE_PL: 'Visible workspaces',
  VISIBILITY_CLUSTER: 'Visible cluster',
  VISIBILITY_CLUSTER_PL: 'Visible clusters',
  // Quick Access
  QUICK_ACCESS: 'Quick Access',
  QUICK_ACCESS_COMING_SOON: 'The quick access feature will be available in the future',
};
