/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  VOLUME_SNAPSHOT_CLASS_PL: 'Volume Snapshot Classes',
  VOLUME_SNAPSHOT_CLASS: 'Volume Snapshot Class',
  VOLUME_SNAPSHOT_CLASS_DESC:
    'Volume snapshot classes provide a way for administrators to define storage types used for volume snapshots.',
  VOLUME_SNAPSHOT_CLASS_EMPTY_DESC: 'Please create a volume snapshot class.',
  // List > Create
  SNAPSHOT_CLASS_SETTINGS: 'Volume Snapshot Class Settings',
  SNAPSHOT_CLASS_PROVISIONER_DESC: 'Enter the name of a CSI plugin.',
  SNAPSHOT_CLASS_PROVISIONER_EMPTY_DESC: 'Please enter the name of a CSI plugin.',
  // List > Delete
  VOLUME_SNAPSHOT_CLASS_LOW: 'volume snapshot class',
  DELETE_VOLUME_SNAPSHOT_CLASS: 'Delete Volume Snapshot Class',
};
