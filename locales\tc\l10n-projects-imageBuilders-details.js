/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  COPIED_SUCCESSFUL: '複製成功',
  BUILDER_IMAGE: 'Builder Image',
  PULL_POLICY: 'Pull Policy',
  REMOTE_TRIGGER: 'Remote Trigger',
  // Run
  S2I_UPDATE_WORKLOAD: 'Update workload after image building is successful',
  S2I_UPDATA_WORKLOAD_DESC:
    'After the image is rebuilt successfully, workloads that use the image will be updated.',
  // More > Edit Information
  // More > Edit YAML
  // More > Delete
  IMAGE_BUILDER: '構建鏡像',
  IMAGE_BUILDER_LOW: 'image builder',
  // Run Records
  JOB_RECORDS: '任務紀錄',
  LAST_BUILD_ENVIRONMENT: '最後一次構建環境',
  BUILDER_IMAGE_SCAP: 'Builder image',
  FILE_SIZE: '檔案大小：{size}',
  DOWNLOAD_ARTIFACT: '下載成品',
  ARTIFACT_FILE: '上傳成品',
  LOG_MODULE_NOT_INSTALLED: '紀錄模組未安裝',
  LOADING_DOTS: '正在載入',
  IMAGE_NAME_SCAP: 'Image name',
  IMAGE_SIZE_SCAP: '鏡像大小',
  IMAGE_NAME_BUILDING: 'Image: {name}/Building',
  IMAGE_NAME_FAILED: 'Image: {name}/Failed',
  IMAGE_NAME_SUCCESSFUL: 'Image: {name}/Successful',
  LAST_MESSAGE_SCAP: 'Last message',
  START_TIME_SCAP: 'Start time',
  // Resource Status
  SOURCE_URL: 'Source URL',
  NEW_TAG: 'New Tag',
  NEW_TAG_DESC: 'Enter the tag of the new image',
  // Image Artifacts
  IMAGE_ARTIFACTS: '鏡像成品',
  RELEASE_TIME_SCAP: 'Release time',
  PULL_COMMAND_SCAP: 'Pull command',
};
