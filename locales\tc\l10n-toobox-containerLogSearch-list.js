/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Title
  TOTAL_LOGS_TODAY:
    'A total of <span class={className}> {logs} </span> log entries from <span class={className}>{containers}</span> containers have been collected today.',
  // Search
  LOGGING_NOT_ENABLED_DESC:
    'The component is not enabled. <a href="{docUrl}/pluggable-components/logging/" target="_blank">Learn More</a>',
  WORKLOAD: 'Workload',
  TIME_RANGE_SCAP: 'Time range',
  // Querying Rules
  QUERYING_RULES: 'Querying Rules',
  CONTAINER_LOG_TRENDS_12H: 'Container log trends in 12 h',
  CONTAINER_LOG_KEYWORD_TIP: 'Enter a keyword to search for container logs.',
  CONTAINER_LOG_PROJECT_TIP: 'Enter a project name to search for container logs.',
  CONTAINER_LOG_WORKLOAD_TIP: 'Enter a workload name to search for container logs.',
  CONTAINER_LOG_CONTAINER_TIP: 'Enter a container name to search for container logs.',
  CONTAINER_LOG_POD_TIP: 'Enter a pod name to search for container logs.',
  SEARCH_BY_KEYWORD: 'Search by Keyword',
  SEARCH_BY_PROJECT: 'Search by Project',
  SEARCH_BY_WORKLOAD: 'Search by Workload',
  SEARCH_BY_POD: 'Search by Pod',
  SEARCH_BY_CONTAINER: 'Search by Container',
  TIME_RANGE: 'Time Range',
};
