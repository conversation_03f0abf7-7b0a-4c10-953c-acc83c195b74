/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  JOB_PL: 'Jobs',
  JOB_DESC:
    'Jobs are used to perform short-lived, one-off tasks. A Job creates one or more Pods and ensures that a specific number of Pods successfully terminate.',
  // List
  JOB_EMPTY_DESC:
    'Jobs are used to perform short-lived, one-off tasks. A <PERSON> creates one or more Pods and ensures that a specific number of Pods successfully terminate.',
  JOB_COMPLETED: 'Completed',
  JOB_FAILED: 'Failed',
  JOB_RUNNING: 'En ejecución',
  LAST_RUN_TIME: 'Last Run Time',
  // List > Create > Basic Information
  // List > Create > Strategy Settings
  // List > Create > Pod Settings
  RESTART_POLICY_NEVER_DESC: 'Re-create pod',
  RESTART_POLICY_ONFAILURE_DESC: 'On failure (restart the container when a Pod fails)',
  // List > Create > Storage Settings
  // List > Create > Advanced Settings
  // List > Edit Information
  // List > Rerun
  RERUN: 'Repetición',
  OPERATION_SUCCESS: 'Operación exitosa',
  OPERATION_FAILED: 'Operación fallida',
  // List > Delete
  JOB: 'Trabajo',
  JOB_LOW: 'Job',
};
