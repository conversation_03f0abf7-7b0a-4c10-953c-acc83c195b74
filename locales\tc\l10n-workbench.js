/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Head
  WORKBENCH: 'Workbench',
  DASHBOARD_TITLE: '您好 {username}',
  LAST_LOGIN_TIME: 'Last Login: ',
  USER_DASHBOARD_EMPTY_TITLE: '您暫時不屬於任何企業空間',
  USER_DASHBOARD_EMPTY_DESC: '您可以聯繫某個企業空間的管理員邀請您加入企業空間中',
  // Platform Information
  LAST_UPDATE_TIME: '上次更新時間',
  PLATFORM_VERSION: '平台版本',
  // Platform Resources
  PLATFORM_RESOURCES: '平臺資源',
  APP_TEMPLATE_SCAP_PL: '應用範本',
  // Recent Access
  RECENT_ACCESS: '最近訪問',
  MULTI_CLUSTER_DEPLOYMENT: '多集群部署',
  WORKBENCH_PROJECT: '項目',
  WORKBENCH_WORKSPACE: '企業空間',
  WORKBENCH_DEVOPS: 'DevOps 项目',
  WORKBENCH_MULTI_CLUSTER_PROJECT: '聯邦項目',
  WORKBENCH_CLUSTER: '集群',
  NOT_FOUND_DESC: '🙇 對不起沒有找到相關資源，系統將在 {time}s 後返回 <a href="{link}">工作台</a>',
  MULTI_CLUSTER_PROJECT_TIP: '該資源部署在多個集群',
  NO_HISTORY_TITLE: 'No Recently Accessed Resource Found',
  NO_HISTORY_DESC: 'You can access platform resources.',
  WORKSPACES_MANAGEMENT_DESC: 'Share resources across clusters in workspaces.',
  PLATFORM_CLUSTER_DESC: 'Manage resources based on infrastructures.',
  USER_AND_ROLE_MANAGEMENT: 'Users and Roles',
  USER_AND_ROLE_MANAGEMENT_DESC: 'Manage users and roles on the platform.',
  EXTENSION_DESC: 'Install, uninstall, configure, and update extensions here.',
  ALL_WORKSPACE: 'Workspace',
  ALL_WORKSPACE_PL: 'Workspaces',
  WORKBENCH_ALL_CLUSTER: 'Cluster',
  WORKBENCH_ALL_CLUSTER_PL: 'Clusters',
  ALL_USER: 'User',
  ALL_USER_PL: 'Users',
  INSTALLED_COMPONENT: 'Installed Extension',
  INSTALLED_COMPONENT_PL: 'Installed Extensions',
  VISIBILITY_WORKSPACE: 'Visible workspace',
  VISIBILITY_WORKSPACE_PL: 'Visible workspaces',
  VISIBILITY_CLUSTER: 'Visible cluster',
  VISIBILITY_CLUSTER_PL: 'Visible clusters',
  // Quick Access
  QUICK_ACCESS: 'Quick Access',
  QUICK_ACCESS_COMING_SOON: 'The quick access feature will be available in the future',
};
