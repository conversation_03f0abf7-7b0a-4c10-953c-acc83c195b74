/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  DEVOPS_PROJECT_SETTINGS: 'DevOps Project Settings',
  DEVOPS_DESCRIPTION:
    'A DevOps project is a separate namespace where a set of pipelines is defined. Users can group their pipelines themselves (for example, project type and organization type).',
  // Basic Information
  DEVOPS_PROJECT_ROLE_SCAP: 'DevOps project role',
  DEVOPS_PROJECT_SCAP: 'DevOps project',
  DEVOPS_PROJECT_MEMBER_SCAP: 'DevOps project member',
  DEVOPS_PROJECT_MEMBER_PL_SCAP: 'DevOps project members',
  DEVOPS_PROJECT_ROLE_PL_SCAP: 'DevOps project roles',
  // Continuous Deployment Allowlist
  CD_ALLOWLIST: 'Continuous Deployment Allowlist',
  CD_ALLOWLIST_SCAP: 'Continuous deployment allowlist',
  DEPLOYMENT_LOCATION_PL: 'Deployment Locations',
  ENABLE_ALLOWLIST: 'Enable Allowlist',
  EDIT_ALLOWLIST: 'Edit Allowlist',
  EMPTY_ALLOWLIST_TITLE: 'Continuous Deployment Allowlist Disabled',
  ALLOWLIST_EMPTY_DESC:
    'Enable the allowlist so that only specific code repositories and deployment locations can be used for continuous deployment.',
  MULTI_CLUSTER_PROJECT_NOT_FOR_CD:
    'Multi-cluster projects cannot be used for continuous deployment.',
  CODE_REPOSITORY_EXIST_DESC:
    'The code repository already exists. Please select another code repository.',
  DEPLOYMENT_LOCATION_EXIST_DESC:
    'The deployment location already exists. Please set another deployment location.',
  CODE_REPOSITORY_NOT_SELECTED: 'No code repositories are selected.',
  RESOURCE_DEPLOYMENT_LOCATION_NOT_SELECTED: 'No resource deployment locations are selected.',
  ALL_CODE_REPOSITORIES: 'All code repositories',
  ALL_RESOURCE_DEPLOYMENT_LOCATIONS: 'All resource deployment locations',
};
