/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  SERVICE_ACCOUNT_PL: '服務帳戶',
  SERVICE_ACCOUNT_DESC:
    '服務帳戶（Service Account）為 Pod 中運行的進程提供了壹個標識，用於訪問 API Server。',
  // List
  SERVICE_ACCOUNT_EMPTY_DESC: 'Please create a service account.',
  // List > Create
  INVALID_YAML_FILE_FORMAT: 'YAML 文件格式錯誤。',
  // List > Create > Project Role
  PROJECT_ROLE_SI: '項目角色',
  SELECT_PROJECT_ROLE_DESC: '選擇一個項目角色以指定權限。',
  // List > Edit
  // List > Edit YAML
  // List > Change Role
  CHANGE_ROLE: '修改角色',
  // List > Delete
  SERVICE_ACCOUNT_LOW: 'service account',
};
