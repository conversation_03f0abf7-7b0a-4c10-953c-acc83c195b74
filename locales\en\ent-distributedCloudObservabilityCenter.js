/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Resource Pie Charts
  GRAPHICS_MEMORY_USAGE: 'Graphics Memory Usage',
  POD_QUOTA_USAGE: 'Pod Quota Usage',
  QUOTA_REQUEST: 'Request',
  QUOTA_LIMIT: 'Limit',
  QUOTA_TOTAL: 'Total',
  CPU_QUOTA_USAGE: 'Allocated CPU Resource',
  MEMORY_QUOTA_USAGE: 'Allocated Memory Resource',
  QUOTA_ALLOCATED: 'Allocated',
  VALUE_CORES: '{value, plural, =1 {1 core} other {# cores}}',
  VALUE_UNIT: '{value} {unit}',
  ALLOCATED_TOTAL: 'Allocated/Total',
  // Cluster Roles and Members
  CLUSTER_ROLES_AND_MEMBERS: 'Cluster Members and Roles',
  CLUSTER_MEMBER_SCAP: 'Cluster member',
  CLUSTER_MEMBERS_SCAP: 'Cluster members',
  CLUSTER_ROLE_SCAP: 'Cluster role',
  CLUSTER_ROLES_SCAP: 'Cluster roles',
  DISTRIBUTED_OBSERVABILITY_CENTER: 'Distributed Observability Center',
  DISTRIBUTED_OBSERVABILITY_CENTER_DESC:
    'Unified monitoring dashboard that provides metrics of all clusters managed by the platform.',
  ALERTING_CENTER: 'Alerting Center',
  SELECT_CLUSTER: 'Select Clusters',
  CUSTOM_PANEL_DESC: 'Select clusters to be monitored.',
  // Resource Usage Ranking
  SORT_BY_CLUSTER_CPU_USAGE: 'Sort by CPU usage',
  SORT_BY_CLUSTER_CPU_UTILISATION: 'Sort by CPU usage (%)',
  SORT_BY_CLUSTER_MEMORY_USAGE_WO_CACHE: 'Sort by memory usage',
  SORT_BY_CLUSTER_MEMORY_UTILISATION: 'Sort by memory usage (%)',
  SORT_BY_CLUSTER_DISK_SIZE_USAGE: 'Sort by disk usage',
  SORT_BY_CLUSTER_DISK_SIZE_UTILISATION: 'Sort by disk usage (%)',
  SORT_BY_CLUSTER_POD_COUNT: 'Sort by number of pods',
  SORT_BY_CLUSTER_POD_UTILISATION: 'Sort by pod quota usage (%)',
  SORT_BY_NODE_POD_COUNT: 'Sort by number of pods',
  SORT_BY_NODE_CPU_USAGE: 'Sort by CPU usage',
  SORT_BY_NODE_MEMORY_USAGE_WO_CACHE: 'Sort by memory usage',
  SORT_BY_NODE_LOAD5: 'Sort by average CPU load (5 min)',
  SORT_BY_NODE_LOAD15: 'Sort by average CPU load (15 min)',
  SORT_BY_POD_CPU_USAGE: 'Sort by CPU usage',
  SORT_BY_POD_CPU_USED_REQUESTS_UTILISATION: 'Sort by allocated CPU request (%)',
  SORT_BY_POD_CPU_USED_LIMITS_UTILISATION: 'Sort by allocated CPU limit (%)',
  SORT_BY_POD_MEMORY_USAGE_WO_CACHE: 'Sort by memory usage',
  SORT_BY_POD_MEMORY_USED_REQUESTS_UTILISATION: 'Sort by allocated memory request (%)',
  SORT_BY_POD_MEMORY_USED_LIMITS_UTILISATION: 'Sort by allocated memory limit (%)',
  SORT_BY_POD_NET_BYTES_TRANSMITTED: 'Sort by outbound traffic',
  SORT_BY_POD_NET_BYTES_RECEIVED: 'Sort by inbound traffic',
  SORT_BY_POD_PVC_BYTES_USED: 'Sort by persistent volume usage',
  SORT_BY_POD_PVC_BYTES_UTILISATION: 'Sort by persistent volume usage (%)',
  SORT_BY_NAMESPACE_CPU_USED_REQUESTS_UTILISATION: 'Sort by allocated CPU request (%)',
  SORT_BY_NAMESPACE_CPU_USED_LIMITS_UTILISATION: 'Sort by allocated CPU limit (%)',
  SORT_BY_NAMESPACE_MEMORY_USED_REQUESTS_UTILISATION: 'Sort by allocated memory request (%)',
  SORT_BY_NAMESPACE_MEMORY_USED_LIMITS_UTILISATION: 'Sort by allocated memory limit (%)',
  SORT_BY_NAMESPACE_PVC_BYTES_USED: 'Sort by persistent volume usage',
  SORT_BY_NAMESPACE_PVC_COUNT: 'Sort by number of pods',
  SORT_BY_POD_GPU_USAGE: 'Sort by GPU usage',
  SORT_BY_POD_GPU_MEMORY_USAGE: 'Sort by graphics memory usage',
  // Resource Usage Ranking > View More
  CPU_USAGE_TCAP: 'CPU Usage',
  CPU_UTILISATION_TCAP: 'CPU Usage (%)',
  MEMORY_USAGE_TCAP: 'Memory Usage',
  MEMORY_UTILISATION_TCAP: 'Memory Usage (%)',
  DISK_SIZE_USAGE_TCAP: 'Disk Usage',
  DISK_SIZE_UTILISATION_TCAP: 'Disk Usage (%)',
  POD_COUNT_TCAP: 'Pods',
  NODE_POD_UTILISATION_TCAP: 'Pod Quota Usage (%)',
  NODE_LOAD1_TCAP: 'Average CPU Load (1 min)',
  NODE_LOAD5_TCAP: 'Average CPU Load (5 min)',
  NODE_LOAD15_TCAP: 'Average CPU Load (15 min)',
  INODE_USAGE_TCAP: 'Inode Usage (%)',
  METER_NET_RECEIVED_USAGE_TCAP: 'Inbound Traffic',
  METER_NET_TRANSMITTED_USAGE_TCAP: 'Outbound Traffic',
  CPU_USED_REQUESTS_UTILISATION_TCAP: 'Allocated CPU Request (%)',
  CPU_USED_LIMITS_UTILISATION_TCAP: 'Allocated CPU Limit (%)',
  MEMORY_USED_REQUESTS_UTILISATION_TCAP: 'Allocated Memory Request (%)',
  MEMORY_USED_LIMITS_UTILISATION_TCAP: 'Allocated Memory Limit (%)',
  PVC_BYTES_USED_TCAP: 'Persistent Volume Usage',
  PVC_BYTES_UTILISATION_TCAP: 'Persistent Volume Usage (%)',
  POD_UTILISATION_TCAP: 'Pod Quota Usage (%)',
  SORT_BY_DESC: 'Descending',
  SORT_BY_ASC: 'Ascending',
  // Pods
  OOM_KILL_PODS: 'OOM killed pods',
  PENDING_PODS: 'Pending pods',
  EVICTED_PODS: 'Evicted pods',
  QOS_GUARANTEED:
    'Guaranteed: For every container in a pod, the CPU/memory request and limit are equal.',
  QOS_BURSTABLE:
    'Burstable: Criteria of Guaranteed is not met and at least one container has a request or limit.',
  QOS_BESTEFFORT: 'BestEffort: No requests or limits are set.',
};
