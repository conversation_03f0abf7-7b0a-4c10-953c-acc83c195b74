/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // More > Roll Back
  // More > Edit Service
  // More > Edit Settings > Update Strategy
  // More > Edit Settings > Containers
  // More > Edit Settings > Containers > Add Container
  // More > Edit Settings > Volumes > Mount Volume
  // More > Edit Settings > Volumes > Mount Configmap or Secret
  // Attributes
  // Resource Status
  // Revision Records
  REVISION_RECORDS_DESC:
    'A revision record is generated after the workload is created or modified, which can be used to roll back the workload settings. A maximum of 10 revision records can be retained.',
  // Metadata
  // Monitoring
  // Environment Variables
  ENVIRONMENT_VARIABLE: 'Environment Variable',
  // Events
};
