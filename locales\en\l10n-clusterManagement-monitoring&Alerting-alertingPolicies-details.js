/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  DURATION: 'Duration',
  // Alert Rules
  ALERTING_RULE: 'Alert Rules',
  MONITORING_TARGETS_SCAP: 'Monitoring targets',
  TRIGGER_CONDITION_SCAP: 'Trigger condition',
  METRIC_MONITORING: 'Metric Monitoring',
  ALERT_MONITORING: 'Alert Monitoring',
  NOTIFICATION_SUMMARY_COLON: 'Summary: ',
  DETAILS_COLON: 'Details: ',
  // Alerting History
  NO_DATA_DESC: 'No Data Found',
};
