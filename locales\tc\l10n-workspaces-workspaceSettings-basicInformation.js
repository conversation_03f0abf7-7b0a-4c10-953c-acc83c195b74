/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Navigation Pane
  WORKSPACE_SETTINGS: '企業空間設置',
  // Banner
  WORKSPACE_BASIC_INFO_DESC:
    'Basic information provides the overview of the workspace. You can view the basic information of the workspace.',
  WORKSPACE_BASE_INFO_Q1: '如何為企業空間申請更多的集群？',
  WORKSPACE_BASE_INFO_A1:
    'Contact the platform or cluster administrator to apply for more clusters.',
  // Workspace Information
  WORKSPACE_INFO: '企業空間資訊',
  WORKSPACE_MEMBER_TCAP: 'Workspace member',
  WORKSPACE_MEMBER_TCAP_PL: 'Workspace members',
  WS_MEMBER_SCAP: 'Workspace member',
  WS_MEMBER_SCAP_PL: 'Workspace members',
  DEVOPS_PROJECT_TCAP: 'DevOps 项目',
  DEVOPS_PROJECT_TCAP_PL: 'DevOps projects',
  DEVOPS_PROJECT_LOW: 'DevOps 项目',
  DEVOPS_PROJECT_LOW_PL: 'DevOps projects',
  PROJECTS: '項目',
  // Workspace Information > Edit Information
  // Network Isolation
  ON: 'On',
  OFF: 'Off',
  WS_NETWORK_ISOLATION: '企業空間網路隔離',
  NETWORK_POLICY_UNINSATLLED_DESC: 'The network policy component is not installed in this cluster.',
  // Delete Workspace
  DELETE_DESC:
    'Are you sure you want to delete the resource? The resource cannot be restored after it is deleted.',
  // Delete Workspace > Delete
  DELETE_WORKSPACE_PROJECTS_DESC: '刪除該企業空間關聯項目',
  DELETE_WORKSPACE_DESC: '刪除後將無法恢復，企業空間下的資源也同時會被銷毀。',
  DELETE_WORKSPACE_TIP:
    '確定刪除企業空間 <strong> {resource} </strong> ？ 刪除後將無法恢復，企業空間下的資源也同時會被銷毀。',
};
