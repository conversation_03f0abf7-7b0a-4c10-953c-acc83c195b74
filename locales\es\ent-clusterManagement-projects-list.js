/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  ANNOTATION_FORMAT_DESC:
    'The annotation key can contain only letters, numbers, hyphens (-), underscores (_), slashes (/), and dots (.), and must start and end with a letter or number. The maximum length is 63 characters (if the key contains a domain name, the maximum length is 253 characters).',
};
