/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  abortPipeline: 'abortPipeline',
  'Absolute duration': 'Absolute duration',
  'Add another credential': 'Add another credential',
  'Add conditions': 'Add conditions',
  'Add nesting conditions': 'Add nesting conditions',
  'Add nesting steps': 'Add nesting steps',
  'Add Parallel Stage': 'Add Parallel Stage',
  'Add Step': 'Add Step',
  archiveArtifacts: 'archiveArtifacts',
  'Are you sure to close this pipeline Editor ?': 'Are you sure to close this pipeline Editor ?',
  artifacts: 'artifacts',
  'Authentication Token': 'Authentication Token',
  'Automatically generated by GitHub': 'Automatically generated by GitHub',
  bcc: 'bcc',
  BEHAVIORAL_STRATEGY: 'Behavioral Strategy',
  blocker: 'blocker',
  BLOCKER: 'Blocker',
  NOTICE: 'Notice',
  Branches: 'Branches',
  'branch success': 'branch success',
  '@somebody to help review': '@somebody to help review',
  'Send messages by email': 'Send messages by email',
  'Send messages in the build': 'Send messages in the build',
  'Change Current Directory': 'Change Current Directory',
  'Chinese is not allowed in the pipeline configuration':
    'Chinese is not allowed in the pipeline configuration',
  'Clean when aborted': 'Clean when aborted',
  'Clean Workspace': 'Clean Workspace',
  'Code Quality Check': 'Code Quality Check',
  COMMIT_ID: 'Commit ID',
  'Config File Path': 'Config File Path',
  'config name': 'config name',
  configs: 'configs',
  'Configuration error': 'Configuration error',
  container: 'container',
  Continue: 'Continue',
  'credential Id': 'credential Id',
  'Credential Id': 'Credential Id',
  credentialsId: 'credentialsId',
  critical: 'critical',
  'Current branch name must match the input value':
    'Current branch name must match the input value',
  'defaultValue -1 means not to discard': 'defaultValue -1 means not to discard',
  'Delete all resources of the deployment file': 'Delete all resources of the deployment file',
  'Deploy resources to the Kubernetes cluster': 'Deploy resources to the Kubernetes cluster',
  "Didn't pass": "Didn't pass",
  dir: 'dir',
  Discard: 'Discard',
  'Discover branches from repository': 'Discover branches from repository',
  DISCOVER_PR_FROM_FORKS: 'Discover PRs from Forks',
  DISCOVER_PR_FROM_ORIGIN: 'Discover PRs from Origin',
  'Discover pull requests from forks': 'Discover pull requests from forks',
  'Discover pull requests from origin': 'Discover pull requests from origin',
  'Docker Container Registry Credentials': 'Docker Container Registry Credentials',
  'Docker Registry URL': 'Docker Registry URL',
  dockerCredentials: 'dockerCredentials',
  'Drag and drop tasks to sort': 'Drag and drop tasks to sort',
  Duration: 'Duration',
  echo: 'echo',
  'Edit Config': 'Edit Config',
  'Edit Credential': 'Edit Credential',
  'Enable Variable Substitution in Config': 'Enable Variable Substitution in Config',
  enableConfigSubstitution: 'enableConfigSubstitution',
  'Enter an expression': 'Enter an expression',
  'Environment name': 'Environment name',
  'Executes the code inside the block with a determined time out limit.':
    'Executes the code inside the block with a determined time out limit.',
  expression: 'expression',
  Failure: 'Failure',
  'For accessing GitHub': 'For accessing GitHub',
  'get token': 'get token',
  info: 'info',
  input: 'input',
  'Inspection results do not affect subsequent tasks':
    'Inspection results do not affect subsequent tasks',
  'instance failed to match at least one schema': 'instance failed to match at least one schema',
  'Internal nested conditions only need to satisfy one':
    'Internal nested conditions only need to satisfy one',
  'Internal nesting conditions must be matched': 'Internal nesting conditions must be matched',
  'Jenkinsfile syntax error, message': 'Jenkinsfile syntax error, message',
  'key File Variable': 'key File Variable',
  keyFileVariable: 'keyFileVariable',
  'Kubeconfig Variable': 'Kubeconfig Variable',
  'Kubernetes Namespace for Secret': 'Kubernetes Namespace for Secret',
  'Kubernetes Secrets': 'Kubernetes Secrets',
  Line: 'Line',
  'Load credentials into environment variables': 'Load credentials into environment variables',
  'Load the sonarqube configuration provided by Jenkins into the Pipeline.':
    'Load the sonarqube configuration provided by Jenkins into the Pipeline.',
  mail: 'mail',
  major: 'major',
  minor: 'minor',
  "Missing one or more required properties: 'name'":
    "Missing one or more required properties: 'name'",
  'Negative prefix': 'Negative prefix',
  NO_BRANCH_FOUND_TIP: 'No branch is found.',
  'No need': 'No need',
  Normal: 'Normal',
  'Not Build': 'Not Build',
  'Not fail build': 'Not fail build',
  'not support edit nested stage': 'not support edit nested stage',
  'passphrase Variable': 'passphrase Variable',
  passphraseVariable: 'passphraseVariable',
  'Password Variable': 'Password Variable',
  passwordVariable: 'passwordVariable',
  PIPELINE_LOW: 'pipeline',
  'Pipeline Configuration': 'Pipeline Configuration',
  'Pipeline List': 'Pipeline List',
  'pipeline syntax error': 'pipeline syntax error',
  'Please add at least one step.': 'Please add at least one step.',
  'Please input images name': 'Please input images name',
  'Please input the credential name.': 'Please input the credential name.',
  'Press enter for the next': 'Press enter for the next',
  'Print message': 'Print message',
  'Pull code by Git': 'Pull code by Git',
  'Pull code by SVN': 'Pull code by SVN',
  Queue: 'Queue',
  Recipient: 'Recipient',
  'Registry Credentials': 'Registry Credentials',
  remote: 'remote',
  'Run Pipeline': 'Run Pipeline',
  'Save Artifact': 'Save Artifact',
  'Repo Scanned Successfully': 'Repo Scanned Successfully',
  WEBHOOK_PUSH: 'Webhook Push',
  secretName: 'secretName',
  secretNamespace: 'secretNamespace',
  SELECT_THIS_REPOSITORY: 'Select This Repository',
  Sender: 'Sender',
  sh: 'sh',
  'Shell commands can be executed in the container':
    'Shell commands can be executed in the container',
  'Show Advanced Settings': 'Show Advanced Settings',
  'show yaml editor': 'show yaml editor',
  'sonar is the default config name.': 'sonar is the default config name.',
  "Sorry, you don't have the permission to do this.":
    "Sorry, you don't have the permission to do this.",
  'Specify a container to add nested tasks to execute inside the container':
    'Specify a container to add nested tasks to execute inside the container',
  'Start the follow-up task after the inspection': 'Start the follow-up task after the inspection',
  'Started By': 'Started By',
  Subject: 'Subject',
  submitter: 'submitter',
  submitterParameter: 'submitterParameter',
  'Text Variable': 'Text Variable',
  'The conditions required to implement the current phase (optional).':
    'The conditions required to implement the current phase (optional).',
  'The environment variable entered before running the pipeline is match the current value.':
    'The environment variable entered before running the pipeline is match the current value.',
  'The label on which to run the Pipeline or individual stage':
    'The label on which to run the Pipeline or individual stage',
  'This name has been used.': 'This name has been used.',
  'Time Used': 'Time Used',
  Timeout: 'Timeout',
  timeout: 'timeout',
  'Timeout after no activity in logs for this block':
    'Timeout after no activity in logs for this block',
  timer: 'timer',
  TRIGGER_REMOTE_BUILD: 'Trigger a remote build (for example, using a script)',
  Unnamed: 'Unnamed',
  'Use the following URL to remotely triggerworkbench the build':
    'Use the following URL to remotely triggerworkbench the build',
  'User types that can trigger builds': 'User types that can trigger builds',
  'username or group name, multiple values ​​used, separated':
    'username or group name, multiple values ​​used, separated',
  'Username Variable': 'Username Variable',
  usernameVariable: 'usernameVariable',
  waitForQualityGate: 'waitForQualityGate',
  Webhook: 'Webhook',
  withCredentials: 'withCredentials',
  withSonarQubeEnv: 'withSonarQubeEnv',
  'Wrong Token': 'Wrong Token',
  'You can execute shell commands or windows batch commands in the build.':
    'You can execute shell commands or windows batch commands in the build.',
  PIPELINE_DESC: `A pipeline is an extensible set of tools that can be combined
    to achieve continuous integration and continuous delivery.
    You can create and manage pipelines on this page.`,
  username_password: 'Username and password',
  login_Plateform: 'Login platform',
  CREDENTIALS_DESC: `Credentials are objects that contain some sensitive data,
    such as username and password, SSH key and Token.
    They are used to provide authentication for pulling code,
    pushing/pulling images, executing SSH scripts, etc. when a pipeline is running.`,
  AUTHENTICATION_TOKEN_TIP: `Enable this option if you need to
    trigger a build by accessing a predefined URL.
    A typical use of this feature is to trigger through the source code
    management system's hook script.
    You need to provide an authorization token in the form of a string so that
    only the person with the authorization token can trigger the remote build.`,
  CRON_TIP: `This field follows the cron syntax (slightly different).
    Specifically, each line contains 5 fields separated by tabs or spaces.
    minutes: the first few minutes of an hour (0-59)
    hours: the first few hours of the day (0-23)
    the days of the month: the first few days of a month (1-31 )
    Month: The first few days of the first few days (0-12), the first few days of the week (0-12),
    0 and 7 are Sundays. `,
  PIPELINES_BASEINFO_DESC: 'Please enter the basic information of the pipeline.',
  PIPELINE_ADVANCE_SETTINGS_DESC:
    'Configure a complex behavior policy for the pipeline (Optional).',
  CREDENTIALS_CREATE_DESC: 'Create credentials for DevOps projects',
  CHECKOUT_DESC: 'Pull code; often used to pull non-git code, such as svn.',
  PRIVATE_IMAGE_DESC: `To deploy from a private image repository,
    you need to create a mirrored repository and then pull the image. `,
  AUTHENTICATION_TOKEN_DESC: `Use the following URL to remotely trigger the build:
    JENKINS_URL / job / JOB_NAME / build? Token =TOKEN_NAME or /buildWithParameters?
    Token = TOKEN_NAME You can choose to append &cause=reason to provide the text that
    will be included in the build reason for the record. `,
  PIPELINE_NO_CONFIG: 'The relevant configuration file was not found in the current pipeline',
  EDIT_CREDENTIAL_DESC:
    'The following form will not display the original credential information. The original information will be overwritten by any new input.',
  pipeline_conditions: 'Conditions',
  CODE_SMELL: 'Code Smell',
  BUG: 'Bug',
  VULNERABILITY: 'Vulnerability',
  SECURITY_HOTSPOT: 'Security Hotspot',
  PIPELINE_CREATE_DESC:
    "Pipeline's task execution will begin after the initialization is complete.",
  PIPELINE_QUEUED_DESC:
    'You need to wait for the agent to start and execute the pipeline (note that if the agent has not started for a long time, please check the agent configuration and cluster resources).',
  waitForQualityGate_desc:
    "Code quality check standards are derived from SonarQube's Quality Gate. If you need to customize the standards, go to SonarQube settings.",
  REVIEW_DESC:
    'When the pipeline runs here, this task will be suspended, and you can choose to continue or terminate after the review.',
  INPUT_DESC:
    'When the pipeline runs here, the task will be suspended, and the creator and @somebody can choose to continue or terminate the pipeline.',
  LoadPrevData_Desc:
    'It was detected that this pipeline was not successfully edited last time. Is the last data loaded to continue editing?',
  withSonarQubeEnv_DESC: 'Quickly locate potential or obvious errors in your code',
  waitForQualityGate_DESC: 'Executed after performing code analysis',
  script_DESC: 'Execute groovy script',
  pipeline_owner:
    'The owner of the DevOps project, with the highest authorization of the project, can perform all operations',
  pipeline_maintainer:
    'The maintainer of the DevOps project can perform credentials and pipeline configuration in the DevOps project',
  pipeline_developer: 'The developer of the DevOps project can trigger and view the pipeline',
  pipeline_reporter:
    'The Observer of the DevOps project can only view the resources of the project',
  PATTERN_PIPELINE_NAME_VALID_NAME_TIP:
    "Invalid name (Support uppercase and lowercase letters, numbers, '_' and '-')",
  GET_GITHUB_TOKEN_DESC: `For accessing GitHub
  <a
    class="float-right"
    href="https://github.com/settings/tokens/new?scopes=repo,read:user,user:email,write:repo_hook"
    target="_blank"
  >
    Get Token
  </a>`,
  PIPELINES_FOOTER_SEE_MORE: 'Go to the branch details page to see more.',
  JENKINS_LINS_ERROR: 'has syntax error at line {line}.', // Concatenated

  'Invalid credential ID': 'Invalid credential ID',
  'Batch Run Fail': 'Batch Run Failed',
  'Run Start': 'Start Running',
  'Choose a Pipeline Template': 'Choose a Pipeline Template',
  // Pipeline Creation Page
  DEVOPS_PROJECT_DESC:
    'DevOps projects are used to group resources and control the resource management permissions of different users.',
};
