/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Head
  APP_STORE: 'App Store',
  HOME_APP_STORE_DESC:
    'The official App Store of KubeSphere provides deployment and management features that allow users to quickly deploy apps with one click based on app templates.',
  // Discover
  DISCOVER: 'Discover',
  NEW_APPS: 'New Apps',
  // App Categories
  APP_CATE_ALL: 'All',
  APP_CATE_UNCATEGORIZED: 'Uncategorized',
  APP_CATE_MESSAGE_QUEUEING: 'Message Queuing',
  APP_CATE_IMAGE_REGISTRY: 'Image Registry',
  APP_CATE_PROMETHEUS_EXPORTER: 'Prometheus Exporter',
  APP_CATE_WEB_SERVER: 'Web Server',
  APP_CATE_NETWORKING: 'Networking',
  APP_CATE_DATABASE_CACHE: 'Database & Cache',
  APP_CATE_STORAGE: 'Storage',
  // List
  MAINTAINER_VALUE: 'Maintainer: {value}',
  LATEST_VALUE: 'Latest: {value}',
  TOTAL_CATE_COUNT: 'Total Apps: {total}',
  SEARCH_BY_NAME: 'Search by name',
};
