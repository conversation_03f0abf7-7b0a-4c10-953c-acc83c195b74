/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  SPRING_CLOUD: 'Spring Cloud',
  // Banner
  MICROSERVICE: 'Microservices',
  MICROSERVICE_PL: 'Microservice',
  MICROSERVICE_DESC:
    'The service registry records the mapping between services and service addresses. In a distributed architecture, services will be registered here. When a service needs to call other services, it will find the address of the service and call it. ',
  WHAT_IS_SERVICE_REGISTRATION_CENTER_Q: 'What is a service registry?',
  WHAT_IS_SERVICE_REGISTRATION_CENTER_A:
    'The service registry is the core functional component of Spring Cloud microservices. The service registry records the mapping relationship between services and service addresses. In a distributed architecture, services will be registered here. When a service needs to call other services, it will find the address of the service and call it. ',
  // Overview
  SERVICE_REGISTRATION_CENTER_ADDRESS: 'Service registry address',
  DOWNLOAD_SAMPLE_CONFIGURATIONS: 'Download sample configuration',
  ALL_SERVICE: 'All services',
  ALL_SERVICE_INSTANCE: 'All instances',
  HEALTHY_SERVICE_INSTANCE: 'Healthy instance',
  // List
  MICROSERVICE_INSTANCE_COUNT: 'Number of instances',
  MICROSERVICE_HEALTHY_INSTANCE_COUNT: 'Number of healthy instances',
  TRIGGER_FLAG: 'Trigger protection threshold',
  MICROSERVICE_EMPTY_DESC:
    'The service registry is the core functional component of Spring Cloud microservices. The service registry records the mapping relationship between services and service addresses. In a distributed architecture, services will be <br>registered here. When a service needs to call other services, it will find the address of the service and call it. ',
  MICROSERVICEINSTANCES: 'Microservice instance',
  MICROSERVICEINSTANCES_EMPTY_DESC: 'No microservice instance records found',
  // Instances List
  MICROSERVICE_INSTANCE_STATUS_RUNNING: 'Running',
  MICROSERVICE_INSTANCE_STATUS_OFFLINE: 'Offline',
  EDIT_WEIGHT: 'Edit weight',
  // List > Create
  CREATE_MICROSERVICE_INSTANCE: 'Create instance',
  // List > Create > Storage Settings
  // List > Create > Advanced Settings
  // List > Edit Information
  // List > Edit YAML
  // List > Delete
};
