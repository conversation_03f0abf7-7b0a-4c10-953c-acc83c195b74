/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Head
  WORKBENCH: 'Workbench',
  DASHBOARD_TITLE: '<PERSON><PERSON> {username}',
  LAST_LOGIN_TIME: 'Last Login: ',
  USER_DASHBOARD_EMPTY_TITLE: 'No perteneces a ningún espacio de trabajo actualmente.',
  USER_DASHBOARD_EMPTY_DESC:
    'Puedes ponerte en contacto con un administrador del espacio de trabajo para que te invite a unirte.',
  // Platform Information
  LAST_UPDATE_TIME: 'Last update time',
  PLATFORM_VERSION: 'Versión de plataforma',
  // Platform Resources
  PLATFORM_RESOURCES: 'Platform Resource',
  APP_TEMPLATE_SCAP_PL: 'App templates',
  // Recent Access
  RECENT_ACCESS: 'Visita reciente',
  MULTI_CLUSTER_DEPLOYMENT: 'Despliegue de multi-cluster',
  WORKBENCH_PROJECT: 'Proyecto',
  WORKBENCH_WORKSPACE: 'Espacio de trabajo',
  WORKBENCH_DEVOPS: 'DevOps project',
  WORKBENCH_MULTI_CLUSTER_PROJECT: 'Multi-cluster project',
  WORKBENCH_CLUSTER: 'Cluster',
  NOT_FOUND_DESC:
    '🙇 Lo sentimos, no se encontraron recursos relacionados, el sistema volverá a <a href="{link}">Workbench</a> después de {time} s',
  MULTI_CLUSTER_PROJECT_TIP: 'El recurso se implementa en varios grupos.',
  NO_HISTORY_TITLE: 'No Recently Accessed Resource Found',
  NO_HISTORY_DESC: 'You can access platform resources.',
  WORKSPACES_MANAGEMENT_DESC: 'Share resources across clusters in workspaces.',
  PLATFORM_CLUSTER_DESC: 'Manage resources based on infrastructures.',
  USER_AND_ROLE_MANAGEMENT: 'Users and Roles',
  USER_AND_ROLE_MANAGEMENT_DESC: 'Manage users and roles on the platform.',
  EXTENSION_DESC: 'Install, uninstall, configure, and update extensions here.',
  ALL_WORKSPACE: 'Workspace',
  ALL_WORKSPACE_PL: 'Workspaces',
  WORKBENCH_ALL_CLUSTER: 'Cluster',
  WORKBENCH_ALL_CLUSTER_PL: 'Clusters',
  ALL_USER: 'User',
  ALL_USER_PL: 'Users',
  INSTALLED_COMPONENT: 'Installed Extension',
  INSTALLED_COMPONENT_PL: 'Installed Extensions',
  VISIBILITY_WORKSPACE: 'Visible workspace',
  VISIBILITY_WORKSPACE_PL: 'Visible workspaces',
  VISIBILITY_CLUSTER: 'Visible cluster',
  VISIBILITY_CLUSTER_PL: 'Visible clusters',
  // Quick Access
  QUICK_ACCESS: 'Quick Access',
  QUICK_ACCESS_COMING_SOON: 'The quick access feature will be available in the future',
};
