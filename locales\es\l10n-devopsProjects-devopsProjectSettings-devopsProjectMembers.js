/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  DEVOPS_PROJECT_MEMBER: 'DevOps Project Member',
  DEVOPS_PROJECT_MEMBER_PL: 'DevOps Project Members',
  DEVOPS_PROJECT_MEM_DESC: 'Manage and assign roles to project members.',
  // List
  DEVOPS_PROJECT_MEMBER_EMPTY_DESC:
    'Please invite a member of the current workspace to the DevOps project.',
  // List > Change Member Role
  // List > Remove Member
  // Invite
  INVITE_MEMBER_DESC_DEVOPS:
    'You can invite members who belong to the workspace to the DevOps project.',
  HOW_TO_INVITE_MEMBER_Q: 'How do I invite members to the project?',
  HOW_TO_INVITE_MEMBER_A:
    'The project administrator or users who have permission to invite project members can invite workspace members to the project.',
};
