/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  // List
  SERVICE_TYPE_TCAP: 'Tipo de servicio',
  // List > Create
  SELECT_SERVICE_TYPE: 'Select Service Type',
  // List > Create > Stateless Service
  // List > Create > Stateless Service > Basic Information
  // List > Create > Stateless Service > Pod Settings
  ADD_PORT: 'Agregar puerto',
  // List > Create > Stateless Service > Volume Settings
  // List > Create > Stateless Service > Advanced Settings
  // List > Create > Stateless Service > Cluster Differences
  // List > Create > Stateful Service > Basic Information
  // List > Create > Stateful Service > Pod Settings
  // List > Create > Stateful Service > Volume Settings
  // List > Create > Stateful Service > Advanced Settings
  EMPTY_LABEL_DESC: 'Please add a label.',
  SPECIFY_NODE: 'Especificar nó',
  SPECIFY_NODE_DESC: 'Especifique um nó que precisa ser associado ao serviço.',
  MAXIMUM_STICKINESS_DURATION_DESC:
    'Set a maximum stickiness duration. The value range is 0 to 86400 and the default value is 10800.',
  SERVICE_ADD_METADATA_DESC: 'Add metadata to the Service.',
};
