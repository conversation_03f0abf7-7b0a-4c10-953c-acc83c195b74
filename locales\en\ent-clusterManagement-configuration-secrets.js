/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  DEFAULT_IMAGE_REGISTRY: 'Default image registry',
  SET_AS_DEFAULT: 'Set as Default',
  SET_AS_DEFAULT_REGISTRY_DESC:
    'Set the image registry as the default image registry. Unless otherwise specified, the system use images from the default image registry to create containers. Only one default image registry is allowed in each project.',
  SET_DEFAULT_REGISTRY_SUCCESSFUL:
    'The registry was set as the default image registry successfully.',
};
