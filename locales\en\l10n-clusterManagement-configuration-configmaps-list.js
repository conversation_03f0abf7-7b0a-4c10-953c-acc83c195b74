/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  CONFIGMAP_DESC:
    'A configmap is often used to store configuration information needed for workloads. Many applications will read the information from configuration files, command line parameters, or environment variables.',
  // List
  FIELDS: 'Fields',
  // List > Create > Basic Information
  // List > Create > Data Settings
  // List > Edit Information
  // List > Edit YAML
  EDIT_YAML: 'Edit YAML',
  // List > Edit Settings
  ENTER_CONFIG_VALUE_DESC:
    'Enter the value of the mapping configuration field or use the content from the file.',
  CONFIG_FIELD_DESC: 'The unique value of the key mapped by the configuration field.',
  // List > Delete
};
