/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Title
  // Navigation Pane > Cluster
  METERING_NOT_ENABLED_DESC: '暫無啟用計量模塊的集群',
  NO_METER_DATA: '新創建的資源，需要等待一小時後才能查看數據',
  // Navigation Pane > Cluster Node
  // Navigation Pane > Cluster Node > Pod
  // Navigation Pane > Checkbox
  EXPORT_BILL: '導出 csv 格式的資源消費記錄',
  // Resource Consumption Statictics
  TOTAL_COST: '總金額({unit})',
  PRICE_CONFIG_DESC: '暫未配置價格信息',
  METER_CPU_USAGE: 'CPU 使用量',
  METER_MEMORY_USAGE: '記憶體使用量',
  METER_VOLUME_USAGE: 'Volume Usage',
  METER_NET_RECEIVED_USAGE: 'Inbound Traffic Usage',
  METER_NET_TRANSMITTED_USAGE: 'Outbound Traffic Usage',
  NET_RECEIVED: '入站流量',
  NET_TRANSMITTED: '出站流量',
  COMPOSING_APP: 'Composed app',
  CLUSTER_NODE_SCAP: 'Cluster node',
  POD_SCAP: '容器組',
  APP_TEMPLATE_SCAP: 'Template-based app',
  COMPOSING_APP_SCAP: 'Composed app',
  DEPLOYMENT_SCAP: '部署',
  STATEFULSET_SCAP: 'StatefulSet',
  DAEMONSET_SCAP: 'DaemonSet',
  WORKSPACE_SCAP: '企業空間',
  CLUSTER_SCAP: '集群',
  PROJECT_SCAP: '項目',
  SERVICE_SCAP: '服務',
  HOST_CLUSTER_SCAP: '主集群',
  MEMBER_CLUSTER_SCAP: 'Member cluster',
  // Consumtion History
  CONSUMPTION_HISTORY: '截止到昨天的消費歷史',
  BILLING_CYCLE: '對帳週期',
  CONSUMER_TRENDS: '消費者趨勢',
  AVERAGE_USAGE: '平均用量',
  TOTAL_CONSUMPTION: '共消費',
  TOTAL_CONSUMPTION_Q: '共消費表示什麼？',
  TOTAL_CONSUMPTION_A: '共消費表示在當前對帳週期中每個計費採樣點的資源用量之和',
  TIMERANGE_MORE_30DAY_MSG: '結束時間與開始時間的間隔大於 30 天時，時間間隔最小為 1 天',
  MAXIMUM_USAGE: '最大用量',
  MINIMUM_USAGE: '最小用量',
  RESOURCE_TYPE: '資源類型',
  // Current Consumption
  CURRRENT_RESOURCE_CONSUMPTION: 'Current Consumption',
  // Current Consumption > Tip
  METER_RESOURCE_DESC: '最近 1 小時的消費統計',
};
