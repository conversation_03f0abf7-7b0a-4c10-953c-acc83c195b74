/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  COMPOSED_APP_PL: 'Composed Apps',
  // List
  NO_COMPOSED_APP_FOUND: 'No Composed App Found',
  COMPOSED_APP_EMPTY_DESC: 'Please create a composed app.',
  // List > Edit Information
  // List > Delete
  // List > Create
  STATEFUL_SERVICE: 'Stateful Service',
  STATELESS_SERVICE: 'Stateless Service',
  CREATE_COMPOSED_APP: 'Create Composed App',
  SAVE_FORM_TIP: 'Please save the current settings first.',
  // List > Create > Edit YAML
  YAML_FILE: 'YAML File',
  CREATE_BY_YAML_DESC: 'Customize the settings in the YAML file.',
  // List > Create > Basic Information
  APPLICATION_GOVERNANCE: 'Application Governance',
  VERSION_DESC:
    'The version can contain only lowercase letters and numbers. The maximum length is 16 characters.',
  APPLICATION_GOVERNANCE_DESC:
    'Enable Application Governace to use the Traffic Monitoring, Grayscale Release, and Tracing features for the app.',
  APP_BASIC_INFORMATION_DESC: 'Set the basic information about the app.',
  // List > Create > Service Settings
  APP_SELECT_SERVICE_TYPE_DESC: 'Create a stateless or stateful service.',
  STATEFUL_SERVICE_DESC: 'Create a service and a statefulset.',
  STATELESS_SERVICE_DESC: 'Create a service and a Deployment.',
  APPLICATION_SERVICE_DESC:
    'Add stateful or stateless services to the app based on your requirements.',
  APP_CREATE_SERVICE_DESC: 'Create a service for the app.',
  // List > Create > Ingress Settings
  ROUTE_SETTINGS: 'Ingress Settings',
  ROUTING_RULES: 'Routing Rules',
  ROUTE_SETTINGS_DESC: 'Set routing rules for the app.',
  ADD_ROUTE_SUCCESS: 'The ingress was added successfully.',
};
