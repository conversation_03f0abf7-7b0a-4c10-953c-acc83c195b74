/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Title

  // Suspend
  SUSPEND: 'Suspend',
  APP_NOTE: 'Note',
  SUSPEND_SUCCESSFUL: 'Suspended successfully.',
  // Release
  RELEASE: 'Release',
  ACTIVATE_SUCCESSFUL: 'Released successfully.',
  // Attributes
  APP_ID: 'App ID',
  CATEGORY: 'Category',
  TYPE: 'Type',
  // Verisons
  DEVELOPER: 'Developer',
  NO_VERSION_INFO_DESC: 'No version information is found.',
  ACTIVATE_VERSION: 'Activate Version',
  CANCEL_SUBMISSION: 'Cancel Submission',
  SUSPEND_VERSION: 'Suspend Version',
  APP_SUSPEND_TIP:
    'The app <strong>{name}</strong> cannot be deployed from the App Store after it is suspended. Are you sure you want to suspend it?',
  APP_RECOVER_TIP:
    'The app <strong>{name}</strong> and the suspended versions will be displayed again in the App Store. Are you sure you want to activate it now?',
  // Verisons > Buttons
  // Versions > Chart Files
  // Versions > App Release
  // Versions > App Instances
  // App Information
  // App Release
  REJECTION_REASON: 'Rejection Reason',
  RELEASE_RECORD: 'Release Record',
  RELEASE_RECORD_EMPTY_DESC: 'No release record is found.',
  // App Instances
};
