/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  APP_REPOSITORY_PL: '應用倉庫',
  APP_REPO: '應用倉庫',
  HOW_TO_USE_APP_REPO_Q: '如何使用應用程序存儲庫？',
  HOW_TO_USE_APP_REPO_A:
    '您需要轉到工作區中的項目。 部署新應用程序時，請選擇 " 從應用程序範本 "，然後在下拉清單中選擇應用程序存儲庫以在存儲庫中部署應用程序。',
  APP_REPO_DESC:
    '應用程序存儲庫是用於存儲應用程序範本的存儲庫。 您可以添加應用程序存儲庫來部署和管理其應用程序。',
  // List
  APP_REPOSITORY_EMPTY_DESC: '請添加應用程序存儲庫。',
  APP_REPO_STATUS_SUCCESSFUL: '成功',
  APP_REPO_STATUS_FAILED: '失敗',
  APP_REPO_STATUS_SYNCING: '同步中',
  APP_REPO_STATUS_NOSYNC: '不同步',
  // List > Add
  ADD_APP_REPO: '添加應用程序存儲庫',
  VALIDATE: '驗證',
  SYNC_INTERVAL: '同步間隔',
  SYNC_INTERVAL_DESC: '設定同步間隔。 該值的範圍為3分鐘到24小時。 預設值0表示沒有同步。',
  SYNC_PERIOD_EMPTY_DESC: '請設定同步間隔。',
  SYNC_INTERVAL_INVALID: '無效值。 請輸入0或正整數。',
  APP_REPO_URL_DESC: '在添加或編輯應用程序存儲庫之前，需要驗證URL。',
  SYNC_INTERVAL_TIP: '該值的範圍為3分鐘到24小時。 請輸入一個有效值。',
  SECONDS: '秒',
  MINUTES: '分鐘',
  HOURS: '小時',
  UNRECOGNIZED_URL: '無法識別的URL。',
  INVALID_CREDENTIAL_FORMAT: '無效的憑據格式。',
  MISSING_ACCESS_KEY_ID: '缺少訪問金鑰ID。',
  MISSING_SECRET_ACCESS_KEY: '缺少秘密訪問金鑰。',
  S_THREE_ACCESS_DENIED: 'S3訪問被拒絕。',
  INVALID_URL_FORMAT: '無效的URL格式。',
  INVALID_HTTP_SCHEME: '無效的HTTP方案。',
  HTTP_ACCESS_DENIED: 'HTTP訪問被拒絕。',
  INVALID_HTTPS_SCHEME: 'HTTPS方案無效。',
  INVALID_TYPE: '無效的類型。',
  INVALID_PROVIDERS: '無效的提供程式。',
  INVALID_REPO_URL: '無效的存儲庫URL。',
  INVALID_S_THREE_SCHEME: '無效的S3方案。',
  // List > Add > URL > s3://
  ACCESS_KEY_ID: '訪問金鑰ID',
  SECRET_ACCESS_KEY: '秘密訪問金鑰',
  // List > Edit
  EDIT_APP_REPO: '編輯應用程序存儲庫',
  INVALID_URL_DESC: '無效的URL。',
  VALID_URL_DESC: '有效的URL。',
  // List > Delete
  APP_REPOSITORY: '應用程序存儲庫',
  APP_REPOSITORY_LOW: '應用程序存儲庫',
};
