/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Title
  TOOLBOX: 'Toolbox',
  TOOLBOX_DESC: 'Provides cluster analysis and control tools.',
  DETAIL_NOT_FOUND_DESC: 'No related resources were found. <a href="{link}">Return to {title}</a>',
  // Description
  // Ayalysis Tools
  ANALYSIS_TOOLS: 'Analysis Tools',
  VIEW_KUBE_CONFIG: 'View or download the kubeconfig file of the current cluster.',
  CONTAINER_LOG_SEARCH_DESC: 'A multi-dimensional container log search tool for resources.',
  RESOURCE_EVENT_SEARCH_DESC: 'A multi-dimensional resource event search tool for resources.',
  AUDIT_LOG_DESC: 'A multi-dimensional audit log search tool for resources.',
  RESOURCE_CONSUMPTION_STATISTICS: 'Resource Consumption Statistics',
  METERING_AND_BILLING_DESC: 'View resource consumption of clusters and workspaces.',
  CONTAINER_LOG_SEARCH: 'Container Log Search',
  RESOURCE_EVENT_SEARCH: 'Resource Event Search',
  AUDIT_LOG_SEARCH: 'Audit Log Search',
  // Control Tool
  CONTROL_TOOL: 'Control Tool',
  TOOLBOX_SHIFT_TIPS: ' 👻 Press "shift+left click" to open kubectl in a new browser window.',
  // Third-Party Tools
  THIRD_PARTY_TOOLS: 'Third-Party Tools',
};
