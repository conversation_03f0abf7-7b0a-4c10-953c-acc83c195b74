/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  abortPipeline: 'abortPipeline',
  'Absolute duration': 'Duración total',
  'Add another credential': 'Agregar otra credencial',
  'Add conditions': 'Agregar condiciones',
  'Add nesting conditions': 'Agregar condiciones anidadas',
  'Add nesting steps': 'Agregar pasos anidados',
  'Add Parallel Stage': 'Agregar pasos paralelos',
  'Add Step': 'Agregar paso',
  archiveArtifacts: 'archiveArtifacts',
  'Are you sure to close this pipeline Editor ?':
    '¿Estás seguro de cerrar este editor de pipeline?',
  artifacts: 'artefactos',
  'Authentication Token': 'Token de autenticación',
  'Automatically generated by GitHub': 'Generado automáticamente por GitHub',
  bcc: 'bcc',
  BEHAVIORAL_STRATEGY: 'Estrategia de comportamiento',
  blocker: 'bloqueador',
  BLOCKER: 'Blocker',
  NOTICE: 'Notice',
  Branches: 'Rama<PERSON>',
  'branch success': 'Rama correcta',
  '@somebody to help review': '¿Puede @alguien ayudar a revisar esto?',
  'Send messages by email': 'Puedes enviar mensajes por correo electrónico.',
  'Send messages in the build': 'Puedes enviar mensajes en la compilación',
  'Change Current Directory': 'Cambiar directorio actual',
  'Chinese is not allowed in the pipeline configuration':
    'El chino no está permitido en la configuración de la pipeline',
  'Clean when aborted': 'Limpiar cuando aborte',
  'Clean Workspace': 'Limpiar entono de trabajo',
  'Code Quality Check': 'Código de control de calidad',
  COMMIT_ID: 'Commit ID',
  'Config File Path': 'Ruta del archivo de configuración',
  'config name': 'nombre de configuración',
  configs: 'configs',
  'Configuration error': 'Error de configuración',
  container: 'contenedor',
  Continue: 'Seguir',
  'credential Id': 'ID de autentificación',
  'Credential Id': 'ID de autentificación',
  credentialsId: 'credenciales',
  critical: 'crítico',
  'Current branch name must match the input value':
    'El nombre de la rama actual debe coincidir con el valor de entrada',
  'defaultValue -1 means not to discard': 'defaultValue -1 significa no descartar',
  'Delete all resources of the deployment file':
    'Eliminar todos los recursos del archivo de despliegue',
  'Deploy resources to the Kubernetes cluster': 'Implementar recursos en el clúster de Kubernetes',
  "Didn't pass": 'No pasó',
  dir: 'directorio',
  Discard: 'Descarte',
  'Discover branches from repository': 'Descubrir ramas desde el repositorio',
  DISCOVER_PR_FROM_FORKS: 'Descubrir PR desde Forks',
  DISCOVER_PR_FROM_ORIGIN: 'Descubrir PR desde Origin',
  'Discover pull requests from forks': 'Descubrir las PR desde forks',
  'Discover pull requests from origin': 'Descubrir las PR desde origin',
  'Docker Container Registry Credentials': 'credenciales de Docker Container Registry',
  'Docker Registry URL': 'URL del Docker Registry',
  dockerCredentials: 'dockerCredentials',
  'Drag and drop tasks to sort': 'Arrastra y suelta tareas para ordenar',
  Duration: 'Duración',
  echo: 'echo',
  'Edit Config': 'Editar la configuración',
  'Edit Credential': 'Editar credenciales',
  'Enable Variable Substitution in Config':
    'Habilitar la sustitución de variables en la configuración',
  enableConfigSubstitution: 'enableConfigSubstitution',
  'Enter an expression': 'Introdir una expresión',
  'Environment name': 'Nombre del entorno',
  'Executes the code inside the block with a determined time out limit.':
    'Ejecuta el código dentro del bloque con un límite de tiempo determinado.',
  expression: 'expresión',
  Failure: 'Fallo',
  'For accessing GitHub': 'Para acceder a GitHub',
  'get token': 'obtener token',
  info: 'informacion',
  input: 'entrada',
  'Inspection results do not affect subsequent tasks':
    'Los resultados de la inspección no afectan las tareas posteriores.',
  'instance failed to match at least one schema':
    'la instancia no ha modido compararse con al menos un esquema',
  'Internal nested conditions only need to satisfy one':
    'Las condiciones anidadas solo necesitan cumplir una condición',
  'Internal nesting conditions must be matched':
    'Las condiciones internas de anidación deben coincidir',
  'Jenkinsfile syntax error, message': 'Error de sintaxis de Jenkinsfile, mensaje',
  'key File Variable': 'variable de archivo clave',
  keyFileVariable: 'keyFileVariable',
  'Kubeconfig Variable': 'Variable de Kubeconfig',
  'Kubernetes Namespace for Secret': 'Namespace de Kubernetes para el Secret',
  'Kubernetes Secrets': 'Secrets de Kubernetes',
  Line: 'Línea',
  'Load credentials into environment variables': 'Cargue credenciales en variables de entorno',
  'Load the sonarqube configuration provided by Jenkins into the Pipeline.':
    'Cargue la configuración de sonarqube proporcionada por Jenkins en la Pipeline.',
  mail: 'correo',
  major: 'mayor',
  minor: 'menor',
  "Missing one or more required properties: 'name'":
    "Falta una o más propiedades requeridas: 'nombre'",
  'Negative prefix': 'Prefijo negativo',
  NO_BRANCH_FOUND_TIP: 'No se encontraron ramas',
  'No need': 'No necesario',
  Normal: 'Normal',
  'Not Build': 'Sin Build',
  'Not fail build': 'Build no fallida',
  'not support edit nested stage': 'no admite editar en la fase anidada',
  'passphrase Variable': 'Variable de passphrase',
  passphraseVariable: 'passphraseVariable',
  'Password Variable': 'Variable de contraseña',
  passwordVariable: 'Variable de contraseña',
  PIPELINE_LOW: 'pipeline',
  'Pipeline Configuration': 'Configuración de Pipeline',
  'Pipeline List': 'Lista de Pipelines',
  'pipeline syntax error': 'error de sintaxis en la Pipeline',
  'Please add at least one step.': 'Por favor agregue al menos un paso.',
  'Please input images name': 'Por favor introduce el nombre de las imágenes',
  'Please input the credential name.': 'Por favor introduce el nombre de la credencial.',
  'Press enter for the next': 'Presione enter para ir al siguiente paso',
  'Print message': 'Imprimir mensaje',
  'Pull code by Git': 'Pull código con Git',
  'Pull code by SVN': 'Pull código con SVN',
  Queue: 'Cola',
  Recipient: 'Recipiente',
  'Registry Credentials': 'Credenciales de registro',
  remote: 'remoto',
  'Run Pipeline': 'Ejecutar pipeline',
  'Save Artifact': 'Guardar artefacto',
  'Repo Scanned Successfully': 'Repo Scanned Successfully',
  WEBHOOK_PUSH: 'Webhook Push',
  secretName: 'secretName',
  secretNamespace: 'secretNamespace',
  SELECT_THIS_REPOSITORY: 'Selecciona este repositorio',
  Sender: 'Remitente',
  sh: 'sh',
  'Shell commands can be executed in the container':
    'Los comandos de shell se pueden ejecutar en el contenedor',
  'Show Advanced Settings': 'Mostrar configuración avanzada',
  'show yaml editor': 'muestre el editor de yaml',
  'sonar is the default config name.': 'sonar es el nombre de configuración predeterminado.',
  "Sorry, you don't have the permission to do this.":
    'Lo sentimos, no tienes permiso para hacer esto.',
  'Specify a container to add nested tasks to execute inside the container':
    'Especifique un contenedor para agregar tareas anidadas para ejecutar dentro del contenedor',
  'Start the follow-up task after the inspection':
    'Inicie la tarea de seguimiento después de la inspección.',
  'Started By': 'Empezado por',
  Subject: 'Tema',
  submitter: 'remitente',
  submitterParameter: 'submitterParameter',
  'Text Variable': 'Variable de texto',
  'The conditions required to implement the current phase (optional).':
    'Las condiciones requeridas para implementar la fase actual (opcional).',
  'The environment variable entered before running the pipeline is match the current value.':
    'La variable de entorno ingresada antes de ejecutar la pipeline coincide con el valor actual.',
  'The label on which to run the Pipeline or individual stage':
    'La etiqueta en la que se ejecuta la pipeline o la fase individual',
  'This name has been used.': 'Este nombre ha sido usado.',
  'Time Used': 'Tiempo utilizado',
  Timeout: 'Se acabó el tiempo',
  timeout: 'se acabó el tiempo',
  'Timeout after no activity in logs for this block':
    'Tiempo de espera agotado tras inactividad en los logs deregistro de este bloque',
  timer: 'temporizador',
  TRIGGER_REMOTE_BUILD: 'Ejecutar una build remota (por ejemplo, usando un script)',
  Unnamed: 'Sin nombre',
  'Use the following URL to remotely triggerworkbench the build':
    'Utilice la siguiente URL para activar remotamente el triggerworkbench en el build',
  'User types that can trigger builds': 'Tipos de usuarios que pueden desencadenar compilaciones',
  'username or group name, multiple values ​​used, separated':
    'nombre de usuario o nombre de grupo, múltiples valores utilizados, separados',
  'Username Variable': 'Variable Username',
  usernameVariable: 'usernameVariable',
  waitForQualityGate: 'waitForQualityGate',
  Webhook: 'Webhook',
  withCredentials: 'con credenciales',
  withSonarQubeEnv: 'withSonarQubeEnv',
  'Wrong Token': 'Token incorrecto',
  'You can execute shell commands or windows batch commands in the build.':
    'Puedes ejecutar comandos de shell o comandos por lotes de Windows en el build.',
  PIPELINE_DESC: `A pipeline is an extensible set of tools that can be combined
    to achieve continuous integration and continuous delivery.
    You can create and manage pipelines on this page.`,
  username_password: 'Credenciales de cuenta',
  login_Plateform: 'Plataforma de inicio de sesión',
  CREDENTIALS_DESC: `Credentials are objects that contain some sensitive data,
    such as username and password, SSH key and Token.
    They are used to provide authentication for pulling code,
    pushing/pulling images, executing SSH scripts, etc. when a pipeline is running.`,
  AUTHENTICATION_TOKEN_TIP: `Enable this option if you need to
    trigger a build by accessing a predefined URL.
    A typical use of this feature is to trigger through the source code
    management system's hook script.
    You need to provide an authorization token in the form of a string so that
    only the person with the authorization token can trigger the remote build.`,
  CRON_TIP: `This field follows the cron syntax (slightly different).
    Specifically, each line contains 5 fields separated by tabs or spaces.
    minutes: the first few minutes of an hour (0-59)
    hours: the first few hours of the day (0-23)
    the days of the month: the first few days of a month (1-31 )
    Month: The first few days of the first few days (0-12), the first few days of the week (0-12),
    0 and 7 are Sundays. `,
  PIPELINES_BASEINFO_DESC: 'Introduce la información básica de la pipeline.',
  PIPELINE_ADVANCE_SETTINGS_DESC:
    'Configure una política de comportamiento compleja para la pipeline (Opcional).',
  CREDENTIALS_CREATE_DESC: 'Crear credenciales para proyectos DevOps',
  CHECKOUT_DESC: 'Código de extracción; a menudo se usa para extraer código no git, como svn.',
  PRIVATE_IMAGE_DESC: `To deploy from a private image repository,
    you need to create a mirrored repository and then pull the image. `,
  AUTHENTICATION_TOKEN_DESC: `Use the following URL to remotely trigger the build:
    JENKINS_URL / job / JOB_NAME / build? Token =TOKEN_NAME or /buildWithParameters?
    Token = TOKEN_NAME You can choose to append &cause=reason to provide the text that
    will be included in the build reason for the record. `,
  PIPELINE_NO_CONFIG: 'El archivo de configuración relevante no se encontró en la pipeline actual',
  EDIT_CREDENTIAL_DESC:
    'El siguiente formulario no mostrará la información de credenciales original. Volver a ingresarlo lo sobrescribirá.',
  pipeline_conditions: 'Condiciones',
  CODE_SMELL: 'Olor de código',
  BUG: 'Insecto',
  VULNERABILITY: 'Vulnerabilidad',
  SECURITY_HOTSPOT: 'Punto de acceso de seguridad',
  PIPELINE_CREATE_DESC:
    'La ejecución de la tarea de Pipeline comenzará después de que se complete la inicialización.',
  PIPELINE_QUEUED_DESC:
    'Debe esperar a que el agente se inicie y ejecute la canalización (tenga en cuenta que si el agente no se ha iniciado durante mucho tiempo, verifique la configuración del agente y los recursos del clúster).',
  waitForQualityGate_desc:
    'Los estándares de control de calidad del código se derivan de la puerta de calidad de SonarQube. Si necesita personalizar los estándares, vaya a la configuración de SonarQube.',
  REVIEW_DESC:
    'Cuando la pipeline se ejecute aquí, esta tarea se suspenderá y podrás optar por continuar o finalizar después de la revisión.',
  INPUT_DESC:
    'Cuando la canalización se ejecuta aquí, la tarea se suspenderá, y el creador y @somebody pueden elegir continuar o finalizar la canalización.',
  LoadPrevData_Desc:
    'Se detectó que esta pipeline no se editó correctamente la última vez. ¿Se cargan los últimos datos para continuar editando?',
  withSonarQubeEnv_DESC: 'Localice rápidamente errores potenciales u obvios en su código',
  waitForQualityGate_DESC: 'Ejecutado después de realizar el análisis de código',
  script_DESC: 'Ejecutar script maravilloso',
  pipeline_owner:
    'El propietario del proyecto DevOps, con la máxima autorización del proyecto, puedes realizar todas las operaciones.',
  pipeline_maintainer:
    'El responsable del proyecto DevOps puedes realizar credenciales y configuración de canalización en el proyecto DevOps',
  pipeline_developer: 'El desarrollador del proyecto DevOps puedes activar y ver la pipeline',
  pipeline_reporter: 'El observador del proyecto DevOps solo puedes ver los recursos del proyecto',
  PATTERN_PIPELINE_NAME_VALID_NAME_TIP:
    'Nombre no válido (admite letras mayúsculas y minúsculas, números, "_" y "-")',
  GET_GITHUB_TOKEN_DESC: `Para acceder a GitHub
    <a
      class="float-right"
      href="https://github.com/settings/tokens/new?scopes=repo,read:user,user:email,write:repo_hook"
      target="_blank"
    >
      Get Token
    </a>`,
  PIPELINES_FOOTER_SEE_MORE: 'Vaya a la página de detalles de la sucursal para ver más.',
  JENKINS_LINS_ERROR: 'tiene un error de sintaxis en la línea {line}.',
  // Concatenated
  'Invalid credential ID': 'Invalid credential ID',
  'Batch Run Fail': 'Batch Run Failed',
  'Run Start': 'Start Running',
  'Choose a Pipeline Template': 'Choose a Pipeline Template',
  // Pipeline Creation Page
  DEVOPS_PROJECT_DESC:
    'DevOps projects are used to group resources and control the resource management permissions of different users.',
};
