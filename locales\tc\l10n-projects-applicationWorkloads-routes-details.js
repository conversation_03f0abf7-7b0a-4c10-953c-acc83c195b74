/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  // Resource Status
  DOMAIN_NAME_VALUE: 'Domain Name: {value}',
  PATH_VALUE: '路徑：{value}',
  RULES: '規則',
  ROUTE_PATH_VALUE: '路徑：<strong>{value}</strong>',
  ROUTE_SERVICE_VALUE: '服務：<strong>{value}</strong>',
  ROUTE_PORT_VALUE: '端口：<strong>{value}</strong>',
  SERVICE_COLON: 'Service: ',
  ACCESS_SERVICE: '訪問服務',
  UNABLE_TO_ACCESS: '無法訪問服務',
  UNABLE_TO_ACCESS_TIP:
    'Make sure that domain name resolution policies have been configured in your DNS server or the hosts file of your client machine.',
  CERTIFICATE_VALUE: 'Certificate: {value}',
  ROUTE_PATH: '路徑',
  ROUTE_PORT: '端口',
};
