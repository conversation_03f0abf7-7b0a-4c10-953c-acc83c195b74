/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  NETWORK: 'Network',
  START_IP_ADDRESS: 'Start IP Address',
  END_IP_ADDRESS: 'End IP Address',
  MASK: 'Mask',
  // More > View YAML
  // More > Assign Workspace
  // More > Delete
  // Workspaces
  WORKSPACES: 'Workspaces',
  MANAGER: 'Manager',
  IPPOOL_WORKSPACE_EMPTY_TIP: 'No workspace is using this pod IP pool.',
  // Pods
};
