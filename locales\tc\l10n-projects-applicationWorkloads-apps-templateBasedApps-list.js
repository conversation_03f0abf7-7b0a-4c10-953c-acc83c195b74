/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  TEMPLATE_BASED_APP_PL: '基於範本的應用',
  APPLICATIONS_DESC: '應用為用戶提供完整的業務功能，由一個或多個特定功能的組件組成。',
  APP_PL: '應用',
  APP_TYPES_Q: '應用類型',
  APP_TYPES_A: 'KubeSphere 支持來自於應用商店和應用倉庫的應用部署(基於 Helm)。',
  HOW_TO_USE_APP_GOVERN_Q: '如何使用應用治理？',
  HOW_TO_USE_APP_GOVERN_A: '使用應用治理需要創建自制應用並對每項服務開啟服務治理功能',
  DEPLOY_SAMPLE_APP: '部署範例應用',
  // List
  NO_TEMPLATE_BASED_APP_FOUND: 'No Template-Based App Found',
  TEMPLATE_BASED_APP_EMPTY_DESC:
    'Please create an app from the KubeSphere App Store or an app template.',
  APP: '應用',
  VERSION: '版本',
  CREATING: '創建中',
  UPGRADING: 'Created',
  DELETING: '刪除中',
  // List > Create
  CREATE_APP: '部署新應用',
  CREATE_APP_DESC:
    '輕量化、可移植、自包含的軟體封裝技術，使應用可以在幾乎任何地方以相同的方式運行。',
  FROM_APP_STORE: '來自應用商店',
  FROM_APP_TEMPLATE: '來自應用模板',
  FROM_APP_STORE_DESC: '來自KubeSphere官方應用商店，提供高質量應用和簡易的部署方式',
  // List > Create > From App Template
  SELECT_APP_REPOSITORY: 'Select app repository',
  CURRENT_WORKSPACE: '來自企業空間',
  FROM_APP_TEMPLATE_DESC: '來自於企業空間的自制應用模板以及應用倉庫中添加的第三方 Helm 應用模板',
  APP_TEMPLATES_MODAL_DESC:
    '應用模板來自於企業空間和第三方的 Helm 應用模板，支持一鍵部署並可通過視覺化的方式在 KubeSphere 中展示並提供部署及管理的功能',
  // List > Create > From App Template > App Information
  // List > Create > From App Template > Chart Files
  // List > Edit
  // List > Delete
  APP_LOW: '應用',
  APP_STATUS_WITH_SUFFIX: '應用實例部署{suffix}',
  APP_DEPLOY_STATUS: '應用部署狀態',
  APP_DEPLOY_LOGS: '應用程式實例部署日誌',
  deployFailed: '部署失敗',
  DEPLOYFAILED: '部署失敗',
};
