/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Permissions
  // Permissions > Network
  PERMIGROUP_NETWORK_MANAGEMENT: 'Network',
  PERMISSION_NETWORK_RESOURCES_VIEW: 'Network Resource Viewing',
  PERMISSION_NETWORK_RESOURCES_MANAGEMENT: 'Network Resource Management',
  // Permissions > Project Resources
  PERMIGROUP_PROJECT_RESOURCES_MANAGEMENT: 'Project Resources',
  PERMISSION_PROJECT_RESOURCES_VIEW: 'Project Resource Viewing',
  PERMISSION_PROJECT_RESOURCES_MANAGEMENT: 'Project Resource Management',
  // Permissions > Storage
  PERMISSION_STORAGECLASSES_VIEW: 'Storage Class Viewing',
  PERMISSION_STORAGECLASSES_MANAGEMENT: 'Storage Class Management',
  PERMISSION_VOLUME_SNAPSHOT_CLASSES_VIEW: 'Volume Snapshot Class Viewing',
  PERMISSION_VOLUME_SNAPSHOT_CLASSES_MANAGEMENT: 'Volume Snapshot Class Management',
  // Permissions > Cluster Resources
  PERMIGROUP_CLUSTER_RESOURCES_MANAGEMENT: 'Cluster Resources',
  PERMISSION_CRD_VIEW: 'Custom Resource Definition Viewing',
  PERMISSION_CRD_MANAGEMENT: 'Custom Resource Definition Management',
  PERMISSION_NODES_VIEW: 'Node Viewing',
  PERMISSION_NODES_MANAGEMENT: 'Node Management',
  PERMISSION_COMPONENTS_VIEW: 'System Component Viewing',
  // Permissions > Cluster Settings
  PERMIGROUP_CLUSTER_SETTINGS: 'Cluster Settings',
  PERMISSION_CLUSTER_SETTINGS_VIEW: 'Cluster Settings Viewing',
  PERMISSION_CLUSTER_SETTINGS_MANAGEMENT: 'Cluster Settings Management',
  // Permissions > Monitoring & Alerting
  PERMISSION_CLUSTER_MONITORING_VIEW: 'Monitoring Information Viewing',
  PERMISSION_CLUSTER_MONITORING_MANAGEMENT: 'Monitoring Information Management',
  // Permissions > Access Control
  PERMISSION_CLUSTER_ROLES_VIEW: 'Role Viewing',
  PERMISSION_CLUSTER_ROLES_MANAGEMENT: 'Role Management',
  PERMISSION_CLUSTER_MEMBERS_VIEW: 'Member Viewing',
  PERMISSION_CLUSTER_MEMBERS_MANAGEMENT: 'Member Management',
};
