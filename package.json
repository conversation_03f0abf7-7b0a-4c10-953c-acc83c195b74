{"name": "kubesphere-console", "version": "4.1.1", "private": true, "repository": {"type": "git", "url": "git@**************:kubesphere/console.git"}, "license": "SEE LICENSE IN LICENSE", "workspaces": ["packages/*", "extensions/*", "server", "locales"], "scripts": {"build": "yarn build:dll && yarn build:locales && yarn build:prod && yarn build:server", "build:dll": "ksc build:dll -s true", "build:ext": "ksc build:ext", "build:locales": "esno scripts/build-locales.js", "build:packages": "esno scripts/build-packages.js", "build:prod": "cross-env dll=true ksc build:prod -s true", "build:server": "NODE_ENV=production webpack --config scripts/webpack.server.js --stats errors-only", "changeset:add": "changeset add", "clean": "rimraf packages/*/{esm,cjs,lib,dist} packages/**/*.tsbuildinfo locales/dist dist **/.DS_Store", "create:ext": "ksc create:ext", "dev": "concurrently -k --raw \"yarn dev:client\" \"yarn dev:server\"", "dev:client": "cross-env NODE_ENV=development ksc dev -s true", "dev:server": "cross-env NODE_ENV=development ksc-", "dev:client:full": "yarn clean ; yarn build:dll ; yarn build:locales ; yarn dev:client", "kubed:bump": "esno scripts/kubed-bump.ts", "lint-all": "concurrently --group --timings --prefix-colors=auto \"yarn:lint:*(!:fix)\"", "lint-all:fix": "concurrently --max-processes=1 --group --timings --prefix-colors=auto \"yarn:lint:*:fix\"", "lint:prettier": "prettier --check --ignore-unknown \"**\"", "lint:prettier:fix": "prettier --write --ignore-unknown \"**\"", "lint:sort-package-json": "yarn lint:sort-package-json:fix -- --check", "lint:sort-package-json:fix": "sort-package-json \"**/package.json\" \"!**/node_modules/**/package.json\"", "pre-commit": "lint-staged --concurrent false", "prepare": "husky install", "release:packages": "changeset version && changeset publish", "release:packages:local": "yarn clean && yarn build:locales && yarn build:server && yarn build:packages && esno scripts/publish-packages.local.ts", "release:packages:snapshot": "bash scripts/release.packages.snapshot.sh", "serve": "NODE_ENV=production node server/server.js"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "resolutions": {"@types/react": "17.0.2", "@types/react-dom": "17.0.2"}, "dependencies": {"@kubed/charts": "^0.2.31", "@kubed/code-editor": "^0.2.31", "@kubed/components": "^0.2.31", "@kubed/diff-viewer": "^0.2.31", "@kubed/hooks": "^0.2.31", "@kubed/icons": "^0.2.31", "@kubed/log-viewer": "^0.2.31", "classnames": "^2.2.6", "dayjs": "^1.10.7", "husky": "^8.0.3", "lerna": "^7.1.4", "lint-staged": "^14.0.1", "lodash": "^4.17.20", "mitt": "^3.0.0", "qrcode.react": "^3.1.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-query": "^3.32.1", "react-router-dom": "^6.22.2", "rimraf": "^3.0.2", "semver": "^7.3.8", "styled-components": "5.3.3"}, "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.8", "@babel/node": "^7.15.8", "@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-proposal-decorators": "^7.15.8", "@babel/plugin-proposal-do-expressions": "^7.14.5", "@babel/plugin-proposal-export-default-from": "^7.14.5", "@babel/plugin-proposal-export-namespace-from": "^7.14.5", "@babel/plugin-proposal-function-sent": "^7.14.5", "@babel/plugin-proposal-json-strings": "^7.14.5", "@babel/plugin-proposal-logical-assignment-operators": "^7.14.5", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.14.5", "@babel/plugin-proposal-numeric-separator": "^7.14.5", "@babel/plugin-proposal-object-rest-spread": "^7.15.6", "@babel/plugin-proposal-optional-chaining": "^7.14.5", "@babel/plugin-proposal-pipeline-operator": "^7.15.0", "@babel/plugin-proposal-private-methods": "^7.14.5", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-proposal-throw-expressions": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-modules-systemjs": "^7.15.4", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.15.8", "@babel/preset-react": "^7.14.5", "@babel/runtime": "^7.15.4", "@changesets/cli": "^2.27.7", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.1", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "@types/js-yaml": "^4.0.5", "@types/lodash": "^4.14.176", "@types/qs": "^6.9.7", "@types/react": "^17.0.2", "@types/react-dom": "^17.0.2", "@types/react-table": "^7.7.8", "@types/semver": "^7.3.13", "@types/styled-components": "^5.1.15", "@types/systemjs": "^6.1.1", "@types/webpack-env": "^1.16.3", "@typescript-eslint/eslint-plugin": "^5.1.0", "@typescript-eslint/parser": "^5.1.0", "autoprefixer": "^9.7.4", "babel-jest": "^25.1.0", "babel-loader": "^8.0.6", "babel-plugin-lodash": "^3.3.4", "babel-plugin-module-resolver": "^4.0.0", "babel-plugin-styled-components": "^1.13.3", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "chalk": "^4.1.2", "clean-deep": "^3.4.0", "clean-webpack-plugin": "^3.0.0", "compression": "^1.7.4", "concurrently": "^8.2.2", "connect-history-api-fallback": "^1.6.0", "copy-webpack-plugin": "^9.0.1", "cross-env": "^7.0.3", "css-loader": "^3.4.2", "css-minimizer-webpack-plugin": "^3.1.1", "eslint": "^7.32.0", "eslint-config-airbnb-typescript": "^14.0.1", "eslint-config-kubesphere": "^1.2.2", "eslint-config-prettier": "^9.1.0", "eslint-friendly-formatter": "^4.0.1", "eslint-import-resolver-webpack": "^0.13.1", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-react": "^7.26.1", "eslint-plugin-react-hooks": "^5.0.0", "esno": "^0.16.3", "execa": "^6.1.0", "fast-glob": "^3.2.11", "file-loader": "^6.0.0", "fork-ts-checker-webpack-plugin": "^6.3.5", "friendly-errors-webpack-plugin": "^1.7.0", "fs-extra": "^10.0.0", "http-proxy-middleware": "^1.0.3", "inquirer": "^8.1.0", "ip": "^1.1.5", "latest-version": "^7.0.0", "lodash-es": "^4.17.21", "mini-css-extract-plugin": "^2.7.6", "no-case": "3.0.4", "nodemon": "^2.0.7", "opn": "^6.0.0", "postcss-loader": "^3.0.0", "prettier": "^3.3.3", "prop-types": "^15.7.2", "qs": "^6.10.3", "react-refresh": "^0.10.0", "rehype-raw": "^7.0.0", "rollup": "^2.73.0", "rollup-plugin-esbuild": "^4.9.1", "rollup-plugin-node-externals": "^4.0.0", "shelljs": "^0.8.4", "sort-package-json": "^2.10.0", "style-loader": "^1.1.3", "systemjs-webpack-interop": "^2.3.7", "terser": "^5.31.1", "terser-webpack-plugin": "^5.2.4", "ts-loader": "^9.2.6", "typescript": "4.5.5", "typescript-plugin-styled-components": "^2.0.0", "url-loader": "^4.0.0", "webpack": "^5.58.2", "webpack-assets-manifest": "^5.0.6", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.9.1", "webpack-dev-server": "^4.3.1", "webpack-merge": "^5.8.0", "webpack-merge-json-plugin": "^0.1.0", "webpackbar": "^5.0.0-3", "yargs": "^17.0.1"}, "engines": {"node": ">= 16.0.0"}}