/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Attributes
  MICROSERVICE_INSTANCE_PL: 'Instance',
  MICROSERVICE_INSTANCE_STATUS: 'Instance status',
  MICROSERVICE_INSTANCE_ID: 'Instance ID',
  // Metadata
  SPRING_CLOUD_METADATA: 'Spring Cloud metadata',
  POD_METADATA: 'Container group metadata',
  // Monitoring
  SPRING_CLOUD_MONITORING: 'Spring Cloud Monitoring',
  JVM_THREADS_LIVE: 'Active threads',
  JVM_THREADS_DAEMON: 'Daemon',
  JVM_THREADS_PEAK: 'Thread peak',
  JVM_MEMORY_USED: 'Used',
  JVM_MEMORY_COMMITTED: 'Currently available',
  JVM_MEMORY_MAX: 'Maximum',
  JVM_MEMORY_USED_METASPACE: 'Initial space',
  // Env
  SPRING_BOOT_CONFIGURATION_INFOMATION: 'Spring Boot configuration information ({resources})',
  PROPERTY_NOT_SET: 'Properties not set',
  POD_ENVIRONMENT_VARIABLE: 'Container group environment variable',
};
