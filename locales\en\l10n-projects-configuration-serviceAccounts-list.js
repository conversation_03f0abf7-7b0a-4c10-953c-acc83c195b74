/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  SERVICE_ACCOUNT_PL: 'Service Accounts',
  SERVICE_ACCOUNT_DESC:
    'A service account provides the processes that run in a pod with an identity that can be used to access the API server.',
  // List
  SERVICE_ACCOUNT_EMPTY_DESC: 'Please create a service account.',
  // List > Create
  INVALID_YAML_FILE_FORMAT: 'Invalid YAML file format.',
  // List > Create > Project Role
  PROJECT_ROLE_SI: 'Project Role',
  SELECT_PROJECT_ROLE_DESC: 'Select a project role to specify permissions.',
  // List > Edit
  // List > Edit YAML
  // List > Change Role
  CHANGE_ROLE: 'Change Role',
  // List > Delete
  SERVICE_ACCOUNT_LOW: 'service account',
};
