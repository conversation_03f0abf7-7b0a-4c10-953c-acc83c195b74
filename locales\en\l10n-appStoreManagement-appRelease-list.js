/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  APP_RELEASE: 'App Release',
  APP_RELEASE_DESC:
    'Manages apps to be released to the App Store. You can view app details and approve or reject app releases.',
  // Unreleased
  UNRELEASED: 'Unreleased',
  APP_REVIEW_EMPTY_DESC:
    'Please create an app template in a workspace and submit the app template for release.',
  APP_STATUS_SUBMITTED: 'Submitted',
  APP_STATUS_PASSED: 'Released',
  APP_STATUS_SUSPENDED: 'Suspended',
  APP_STATUS_REJECTED: 'Rejected',
  APP_STATUS_ACTIVE: 'Activated',
  APP_STATUS_PUBLISHED: 'Released',
  APP_STATUS_RECALLED: 'Suspended',
  APP_STATUS_RECALL: 'Recall',
  APP_STATUS_DRAFT: 'To be submitted',
  APP_STATUS_PENDING_REVIEW: 'To be released',
  APP_STATUS_IN_REVIEW: 'Reviewing',
  APP_STATUS_DEVELOPING: 'Developing',
  // Released
  RELEASED: 'Released',
  OPERATOR: 'Operator',
  // All
};
