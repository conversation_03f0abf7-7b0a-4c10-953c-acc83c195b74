/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  CONTAINER_LOAD_CPU_USAGE: 'Container CPU usage',
  CONTAINER_LOAD_MEMORY_USAGE: 'Container memory usage',
  PROJECT_CPU_USAGE: 'Project CPU usage',
  PROJECT_MEMORY_USAGE: 'Project memory usage',
  CPU_TOTAL: 'Total CPU',
  CPU_RESERVATION: 'CPU reservation',
  RESOURCE_RATIO: 'Resource ratio',
  TENANT: 'Tenant',
  COMPARED: 'Compared',
  MEMORY_TOTAL: 'Total Memory',
  MEMORY_RESERVATION: 'Memory Reservation',
  EXCLUDE_THE_MASTER_NODE: 'Exclude the master node',
  RESOURCE_THRESHOLD_SETTINGS: 'Resource Threshold Settings',
  RESOURCE_UTILIZATION_ALARM_THRESHOLD: 'Resource utilization alarm threshold (%)',
  RESERVE_ALARM_THRESHOLD: 'Reserve Alarm Threshold (%)',
  LIMIT_ALARM_THRESHOLD: 'Limit Alarm Threshold (%)',
  // Resource Usage Ranking
  GPU_USAGE_SCAP: 'GPU usage',
  GPU_MEMORY_USAGE_SCAP: 'Graphics memory usage',
  GPU_TEMP: 'GPU temperature',
  GPU_ENERGY_USAGE: 'GPU power usage',
  GPU_ENERGY_NO_PERCENT_TCAP: 'GPU Power',
  GPU_MEMORY_USAGE_STATUS: 'GPU memory usage status',
  GPU_UTILIZATION: 'GPU usage',
  GPU_MEMORY_UTILIZATION: 'Graphics memory usage',
  GPU_USAGE: 'GPU usage',
  GPU_MEMORY_USAGE: 'Graphics memory usage',
  NODE_GPU_MEMORY_USAGED: 'Used Graphics memory',
  NODE_GPU_MEMORY_UNUSAGED: 'Free Graphics memory',
  NODE_GPU_UTILIZATION: 'GPU usage',
  NODE_GPU_MEMORY_UTILIZATION: 'Graphics Memory usage',
  SORT_BY_NODE_GPU_UTILIZATION: 'Sort by GPU usage',
  SORT_BY_NODE_GPU_MEMORY_UTILIZATION: 'Sort by Graphics Memory usage',
  SORT_BY_NAMESPACE_GPU_USAGE: 'Sort by GPU usage',
  SORT_BY_NAMESPACE_GPU_MEMORY_USAGE: 'Sort by Graphics Memory usage',
  SORT_BY_WORKLOAD_GPU_USAGE: 'Sort by GPU usage',
  SORT_BY_WORKLOAD_GPU_MEMORY_USAGE: 'Sort by Graphics Memory usage',
  SORT_BY_WORKSPACE_GPU_USAGE: 'Sort by GPU usage',
  SORT_BY_WORKSPACE_GPU_MEMORY_USAGE: 'Sort by Graphics Memory usage',
  SORT_BY_NODE_GPU_USAGE: 'Sort by GPU usage',
  SORT_BY_NODE_GPU_MEMORY_USAGE: 'Sort by graphics memory usage',
  // Resource Monitoring
  RESOURCE_MONITORING: 'Resource Monitoring',
};
