/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  MICROSERVICE_GATEWAY: 'Microservice gateway',
  MICROSERVICE_GATEWAY_PL: 'Microservice Gateway',
  MICROSERVICE_GATEWAY_DESC:
    'As the entrance of the background architecture, the microservice gateway provides functions such as routing and forwarding, API management, and access filters, and is an important component in the microservice architecture. ',
  WHAT_IS_MICROSERVICE_GATEWAY_Q: 'What is a microservice gateway? ',
  WHAT_IS_MICROSERVICE_GATEWAY_A:
    'As the entrance of the background architecture, the microservice gateway provides functions such as routing and forwarding, API management, and access filters, and is an important component in the microservice architecture. ',
  // Microservice Gateway
  MICROSERVICE_GATEWAY_EMPTY_DESC: 'Create a microservice gateway',
  MICROSERVICE_GATEWAY_EMPTY_TIP: 'You can create a microservice gateway. ',
  SERVICE_ROUTING_PL_EMPTY_DESC:
    'The microservice gateway needs to be created before creating the route.',
  GATEWAY_TYPE: 'Gateway type',
  MICROSERVICE_GATEWAY_STATUS_RUNNING: 'Running',
  MICROSERVICE_GATEWAY_STATUS_UPDATING: 'Updating',
  MICROSERVICE_GATEWAY_STATUS_OFFLINE: 'Offline',
  // Microservice Gateway > Create Microservice Gateway
  CREATE_MICROSERVICE_GATEWAY: 'Create a microservice gateway',
  GATEWAY_MIRRORING: 'Gateway mirroring',
  GATEWAY_MIRRORING_EMPTY_DESC: 'Please fill in the gateway image',
  REPLICA_COUNT_EMPTY_DESC: 'Please fill in the number of copies',
  // Manage > View Details
  // Manage > Edit
  // Manage > Delete
  MICROSERVICE_GATEWAY_LOW: 'Microservice Gateway',
};
