/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  SERVICE_ACCOUNT_PL: 'Cuentas de servicio',
  SERVICE_ACCOUNT_DESC:
    'La cuenta de servicio proporciona una identidad para los procesos que se ejecutan en un pod que se puede usar para acceder al servidor de API',
  // List
  SERVICE_ACCOUNT_EMPTY_DESC: 'Please create a service account.',
  // List > Create
  INVALID_YAML_FILE_FORMAT: 'Formato de archivo YAML no válido.',
  // List > Create > Project Role
  PROJECT_ROLE_SI: 'Rol del proyecto',
  SELECT_PROJECT_ROLE_DESC: 'Seleccione un rol de proyecto para especificar permisos.',
  // List > Edit
  // List > Edit YAML
  // List > Change Role
  CHANGE_ROLE: 'Cambiar rol',
  // List > Delete
  SERVICE_ACCOUNT_LOW: 'service account',
};
