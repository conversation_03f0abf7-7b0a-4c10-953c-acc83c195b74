name: npm Release

on:
  push:
    branches:
      # - ksc
      - ksc-release-4.1
  workflow_dispatch:

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    runs-on: self-runner-kubesphere

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 16

      - name: Install Yarn
        run: npm install -g yarn

      - name: Install Dependencies
        run: yarn install

      - name: Build
        run: |
          yarn build:locales
          yarn build:server
          yarn build:packages

      - name: Create Release Pull Request or Publish to npm
        id: changesets
        uses: changesets/action@v1
        with:
          publish: yarn release:packages
          commit: 'chore(npm-release): version packages'
          title: 'chore(npm-release): version packages'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
