/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  REQUESTS_FOR_PUSH_AN_ALARM_Q: 'How do I receive alerts when alerts are generated?',
  REQUESTS_FOR_PUSH_AN_ALARM_A:
    'A platform administrator needs to configure notification channels. When resource metrics meet conditions and durations configured in rule groups, the system sends alerts to users through the notification channels.',
  // List
  // List > Create > Basic Information
  // List > Create > Rule Settings > Rule Template
  SELECT_WORKLOAD_TIP: 'Please select at lease a workload.',
  KUBE_NO_AVAILABLE_DATA: 'No hay datos disponibles',
};
