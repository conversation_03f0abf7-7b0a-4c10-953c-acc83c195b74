/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // More > Roll Back
  // More > Edit Service
  // More > Edit Settings > Update Strategy
  // More > Edit Settings > Containers
  // More > Edit Settings > Containers > Add Container
  // More > Edit Settings > Volumes > Mount Volume
  // More > Edit Settings > Volumes > Mount Configmap or Secret
  // Attributes
  // Resource Status
  // Revision Records
  REVISION_RECORDS_DESC:
    '對工作負載的資源模板進行修改後會生成一個新的紀錄並重新調度 容器組（Pod）進行版本的疊代，預設保存10個最近的版本。您可以根據修改紀錄進行重新部署。',
  // Metadata
  // Monitoring
  // Environment Variables
  ENVIRONMENT_VARIABLE: '環境變量',
};
