/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // More > Add Component
  // More > Add Component > Create Service
  // More > Delete
  // Attributes
  // Resource Status
  APPLICATION_GOVERNANCE_SCAP: 'Application governance',
  NO_SERVICE_FOUND: 'No Service Found',
  // External Access
  GATEWAY_IP_ADDRESS: 'IP del gateway',
  GATEWAY_ACCESS_MODE: 'Tipo de gateway',
  NODE_PORTS_SCAP: 'Node ports',
  LOAD_BALANCER_SCAP: 'Load balancer',
  LOAD_BALANCERS_SCAP: 'Load balancers',
  // Traffic Management
  TRAFFIC_MANAGEMENT: 'Gestión del tráfico',
  // Tracing
  APPLICATION_GOVERNANCE_ENABLED: 'Microservicio habilitado',
  APPLICATION_GOVERNANCE_DISABLED: 'Microservicio no habilitado',
};
