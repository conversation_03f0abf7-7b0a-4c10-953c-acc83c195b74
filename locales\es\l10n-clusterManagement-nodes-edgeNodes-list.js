/*
 * Please refer to the LICENSE file in the root directory of the project.
 * https://github.com/kubesphere/console/blob/master/LICENSE
 */

module.exports = {
  // Banner
  EDGE_NODE_PL: 'Nó de Borda',
  EDGE_NODE_DESC:
    'Este módulo gestiona los nodos del borda y muestra el estado de ejecución de ellos. Puedes editar o eliminar nodos aquí.',
  // Node Count
  // List
  EDGE_NODE_EMPTY_DESC:
    'Este módulo gestiona los nodos del borda y muestra el estado de ejecución de ellos. Puedes editar o eliminar nodos aquí.',
  AGENT: 'Agent',
  EDGE: 'Edge node',
  // List > Add
  ADD: 'Añadir',
  ADD_EDGE_NODE: 'Add Edge Node',
  EDGENODE_CONFIG_COMMAND: 'Agregar comando',
  ADD_EDGE_COMMAND: 'Run the above command on your edge node to configure it.',
  IN_USE_Node_IP: 'Node IP {ip} in use',
  IN_USE_Node_NAME: 'Node name {name} in use',
  EDGENODE_NAME_EMPTY_DESC: 'Please set a name for the node.',
  EDGENODE_CONFIG_COMMAND_TIP:
    'Before running the command, you must install a container runtime such as Docker or containerd on your edge node. See the KubeEdge <a href="https://kubeedge.io/en/docs/" target="_blank">documentation</a> for more details.',
  ADD_DEFAULT_TAINT: 'Adicionar mancha padrão {params}',
  EDGE_NODE: 'Edge Node',
  INTERNAL_IP_ADDRESS: 'Node Internal IP Address',
  EDGENODE_INTERNAL_IP_DESC: 'The internal IP address for the connection between cluster nodes.',
  EDGENODE_INTERNAL_IP_EMPTY_DESC:
    'Please set the IP address of the edge node in the KubeSphere cluster.',
  COPY_SUCCESSFUL: 'Copied successfully.',
  // List > View Log
  LOGS: 'Registros',
  VIEW_LOG: 'Mostrar log',
};
